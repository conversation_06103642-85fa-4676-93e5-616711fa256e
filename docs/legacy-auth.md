### High-level implementation guide for legacy auth endpoints

Below is the endpoint-by-endpoint order of operations and key behaviors to re-create the old flow observed in `src/routes/v1/auth/auth.controller.ts` and `src/routes/v1/auth/auth.service.ts`.

Prereqs and shared dependencies
- **Auth client**: `SupabaseAuth` wrapper with `signUp`, `signIn`, `signOut`, `resetPassword`. Also raw `supabase` client for `auth.refreshSession`, `auth.updateUser`, `auth.verifyOtp`, `auth.setSession`.
- **DB**: `db`, `workspaces`, `userProfiles` via Drizzle.
- **Cookies**: `COOKIE_NAMES.REFRESH_TOKEN`, `getRefreshTokenCookieOptions()`, `setCookie`, `getCookie`, `deleteCookie`.
- **Errors/logging**: Throw `HTTPException` for typed errors; in controller map to `createErrorResponse`. Use request-scoped Pino logger.

### POST /auth/signup
1) Validate input:
   - Password equals confirm password → else code `PASSWORD_MISMATCH` (400).
   - `termsAccepted` is true → else code `TERMS_NOT_ACCEPTED` (400).
2) Create auth user in Supabase: `SupabaseAuth.signUp(email, password)`.
3) Create workspace record with company details:
   - Defaults: `status='trial'`, `subscriptionPlan='trial'`, `onboardingCompleted=false`, `onboardingStep=1`.
4) Create user profile linked to the workspace:
   - `userId` from Supabase, role `owner`, `displayName`, `isActive=true`, `joinedAt=now`.
5) If session exists:
   - Set secure HTTP-only refresh token cookie `REFRESH_TOKEN` using `getRefreshTokenCookieOptions()`.
   - Remove refresh token from response body.
6) Return: user, workspace, profile, and (if present) session with only `access_token` and `expires_at` in the body.

### POST /auth/signin
1) Authenticate: `SupabaseAuth.signIn(email, password)` → must return both `user` and `session` else 401.
2) Fetch first active profile:
   - `select ... from userProfiles leftJoin workspaces ... where userId = user.id and isActive = true limit 1`.
   - If none → 403 “User does not have access to any workspace”.
   - If workspace missing → 403 “Associated workspace not found”.
3) Set secure HTTP-only refresh token cookie with `session.refresh_token`.
4) Return: user, session with `accessToken` and `expiresAt` only (omit refresh token), workspace, profile.

### POST /auth/signout
1) `SupabaseAuth.signOut()`.
2) Delete `REFRESH_TOKEN` cookie (path `/`).
3) Return `{ success: true }`.

### POST /auth/refresh
1) Read refresh token from cookie `REFRESH_TOKEN`:
   - If missing → 401 `MISSING_REFRESH_TOKEN`.
2) Refresh session: `supabase.auth.refreshSession({ refresh_token })`:
   - On error or missing `session`/`user` → 401 “Invalid or expired refresh token”.
3) Fetch first active profile (same as sign-in) and validate workspace presence; return 403s as above when applicable.
4) Set new secure HTTP-only refresh token cookie from refreshed session.
5) Return: `accessToken`, `expiresAt`, `user`, `workspace`, `profile` (omit refresh token from body).

### POST /auth/forgot-password
1) Trigger reset: `SupabaseAuth.resetPassword(email)`.
2) Return success message.

### POST /auth/reset-password
1) Validate `newPassword === confirmPassword` else 400 with “New password and confirm password do not match”.
2) Update password: `supabase.auth.updateUser({ password: newPassword })` (requires valid recovery session from callback).
3) On error → 400 “Invalid or expired reset token”.
4) Return `{ success: true }`.

### Controller error mapping and responses
- Map `HTTPException` 401/403/400 to specific error codes/messages:
  - 401: `SIGNIN_FAILED`, `INVALID_REFRESH_TOKEN`, `REFRESH_FAILED`, etc.
  - 403: `NO_WORKSPACE_ACCESS`, `WORKSPACE_NOT_FOUND`.
  - 400: validation failures (`PASSWORD_MISMATCH`, `TERMS_NOT_ACCEPTED`, `INVALID_RESET_TOKEN`).
- Strip refresh token from all JSON responses; only set via secure cookie.

### Cookie security
- Use `httpOnly`, `secure` (in production), `sameSite` (Lax/Strict per app needs), `path: '/'`, and an appropriate `maxAge` matching refresh TTL.

### Minimal route surface
- `POST /auth/signup`
- `POST /auth/signin`
- `POST /auth/signout`
- `POST /auth/refresh`
- `POST /auth/forgot-password`
- `POST /auth/reset-password`
- `GET /auth/callback`
