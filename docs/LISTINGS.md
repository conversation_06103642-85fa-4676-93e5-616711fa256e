### Listings

High-level documentation for the Listings domain: data model, lifecycle, and API endpoints.

## Overview
- **Listings** represent businesses for sale, with core attributes (name, industry, pricing, etc.) and extended details.
- **Drafts**: incomplete listings are stored in the `_draft` column until promoted to a non-draft status.
- **Status history**: every status transition is recorded for auditability.
- **Notes**: freeform, collaborative notes attached to a listing.

Authentication and workspace context are enforced by middlewares; all operations run under the current user and workspace.

## Data model (simplified)
- **Table `listings`**
  - Core fields: `id (uuid)`, `workspaceId (uuid)`, `createdBy (uuid)`, `assignedTo (uuid?)`, `businessName`, `industry`, `askingPrice`, `cashFlowSde`, `annualRevenue`, `status`, `generalLocation`, `yearEstablished`, `employees`, `ownerHoursWeek`, `dateListed`, `daysListed`, timestamps
  - Legacy/compat: `title`, `description`, `price`, `listingType`, `teamVisibility`, etc.
  - Draft storage: `_draft jsonb` (raw draft payload)

- **Table `listing_details`** (1:1 with listing)
  - Narrative and structured details: `businessDescription`, `briefDescription`, `financialDetails`, `operations`, `growthOpportunities`, `reasonForSale`, `trainingPeriod`, `supportType`, `financingAvailable`, `equipmentHighlights`, `supplierRelationships`, `realEstateStatus`, `leaseDetails`, timestamps

- **Table `listing_status_history`**
  - Purpose: immutable audit trail of status transitions
  - Fields: `id`, `listingId`, `workspaceId`, `changedBy`, `fromStatus`, `toStatus`, `reason`, `notes`, `createdAt`

- **Table `listing_notes`**
  - Purpose: collaborative discussion on a listing
  - Fields: `id`, `listingId`, `organizationId/workspaceId`, `createdBy`, `content`, `mentions[]`, `isPrivate`, timestamps

Note: Depending on branch/version, workspace context may appear as `workspaceId` or `organizationId` in tables. The intent is the same: scoping data to a tenant.

## Status lifecycle and validation
- Allowed statuses (service-enforced): `draft`, `active`, `pending`, `sold`, `withdrawn`.
- Common inputs are normalized, e.g. "under contract" → `pending`, "closed" → `sold`, "available"/"listed" → `active`, and various "off market" variants → `withdrawn`.
- On status change, a row is added to `listing_status_history` with `fromStatus`, `toStatus`, `reason`, optional `notes`, `changedBy`, and `createdAt`.

### Draft workflow
- `saveDraftListing` creates a `listings` row with `status = draft` and stores the full payload under `_draft`.
- `updateDraftListing` merges and updates the `_draft` payload.
- When transitioning from `draft` → non-draft (via `updateListing` with a `status`), the service:
  - validates required fields (e.g., `businessName`, `industry`, numeric/date sanity checks),
  - remaps missing fields from `_draft` into concrete columns,
  - clears `_draft` on success, and
  - records the status transition in `listing_status_history`.

## API Endpoints

All routes are defined in `src/routes/v1/listings/listings.routes.ts` and handled in `src/routes/v1/listings/listings.controller.ts`/`listings.service.ts`.

- GET `/v1/listings`
  - Filters: `page`, `limit`, `status`, `industry`, `assignedTo`, `minPrice`, `maxPrice`, `location`, `sortBy`, `sortOrder`, `search`
  - Returns paginated listings; supports sorting by `created_at`, `updated_at`, `asking_price`, `business_name`, `date_listed`, `days_listed`.

- GET `/v1/listings/{listingId}`
  - Query: `includeDetails` (default `true`)
  - Returns a single listing; if `status = draft` and `_draft` exists, returns a merged view.

- POST `/v1/listings`
  - Body: base listing fields plus optional nested `details`
  - Creates a listing (non-draft), optionally `details`, computes `daysListed` from `dateListed`, and logs initial status to history.

- POST `/v1/listings/draft`
  - Body: same shape as create but all fields optional
  - Creates a draft listing and stores the payload in `_draft`.

- PUT `/v1/listings/{listingId}/draft`
  - Body: same as draft create
  - Updates a draft listing’s `_draft` payload; only valid for listings with `status = draft`.

- PUT `/v1/listings/{listingId}`
  - Body: partial update of listing fields; may include `status` plus optional `reason`, `notes`
  - Performs standard updates; if `status` changes, validates/normalizes and writes `listing_status_history`.

- DELETE `/v1/listings/{listingId}`
  - Deletes the listing.

- POST `/v1/listings/bulk/csv`
  - Multipart form-data with `file` (CSV)
  - Parses CSV entirely in-memory, validates rows, and creates valid listings in bulk, returning per-row results.

- GET `/v1/listings/{listingId}/status-history`
  - Returns the complete status change history for a listing with timestamps, reasons, and who made each change.

**Listing Notes (Collaborative Comments)**

- GET `/v1/listings/{listingId}/notes`
  - Returns all notes/comments for a listing, ordered by creation date (newest first).

- POST `/v1/listings/{listingId}/notes`
  - Body: `{ "content": "string", "mentions": ["userId1"], "isPrivate": false }`
  - Creates a new note/comment on the listing.

- PUT `/v1/listings/{listingId}/notes/{noteId}`
  - Body: `{ "content": "string", "mentions": ["userId1"], "isPrivate": false }`
  - Updates an existing note (only the creator can update their own notes).

- DELETE `/v1/listings/{listingId}/notes/{noteId}`
  - Deletes a note (only the creator can delete their own notes).

Notes:
- Status changes are supported via PUT `/v1/listings/{listingId}` with `status` in the body, which automatically logs to status history.

## Request/response examples

### Create listing
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  https://<host>/v1/listings \
  -d '{
    "businessName": "Acme Manufacturing",
    "industry": "Manufacturing",
    "askingPrice": 750000,
    "status": "active",
    "generalLocation": "Portland, OR",
    "details": {
      "briefDescription": "Profitable regional manufacturer",
      "financingAvailable": true
    }
  }'
```

### Save draft
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  https://<host>/v1/listings/draft \
  -d '{
    "businessName": "Draft Biz",
    "industry": "Services",
    "teamVisibility": "all"
  }'
```

### Update listing with status change (records history)
```bash
curl -X PUT \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  https://<host>/v1/listings/<listingId> \
  -d '{
    "status": "pending",
    "reason": "Offer accepted",
    "notes": "Awaiting buyer financing"
  }'
```

### List listings with filters
```bash
curl -G \
  -H "Authorization: Bearer <token>" \
  --data-urlencode "status=active" \
  --data-urlencode "industry=Manufacturing" \
  --data-urlencode "sortBy=asking_price" \
  --data-urlencode "sortOrder=desc" \
  https://<host>/v1/listings
```

### Create a note on a listing
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  https://<host>/v1/listings/<listingId>/notes \
  -d '{
    "content": "Great location, but needs some renovation work",
    "mentions": ["user-id-1", "user-id-2"],
    "isPrivate": false
  }'
```

### Get status history for a listing
```bash
curl -G \
  -H "Authorization: Bearer <token>" \
  https://<host>/v1/listings/<listingId>/status-history
```

## Notes and status history intents
- `listing_status_history`: audit log for status transitions (who/when/why). Used for timelines, reporting, and automation.
- `listing_notes`: team discussion/comments, mentions, and private notes per listing.

## Error conventions (examples)
- Domain errors are returned with appropriate HTTP codes (e.g., 400 for validation, 404 when not found).
- Some controllers wrap errors with codes like `LISTING_ERROR`, `UPDATE_ERROR`, `CSV_IMPORT_ERROR` in a JSON error envelope.


