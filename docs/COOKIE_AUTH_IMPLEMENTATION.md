# Cookie-Based Refresh Token Implementation

## Overview

Updated the authentication system to store refresh tokens in secure, httpOnly cookies instead of returning them in response bodies. This significantly improves security by preventing XSS attacks from accessing refresh tokens.

## Security Improvements

### Before
- Refresh tokens were returned in JSON response bodies
- Client-side JavaScript could access refresh tokens
- Vulnerable to XSS attacks
- Required manual token management by clients

### After
- Refresh tokens stored in secure httpOnly cookies
- JavaScript cannot access refresh tokens (httpOnly=true)
- HTTPS-only transmission in production (secure=true)
- CSRF protection with SameSite=Strict
- Automatic cookie management by browser

## Cookie Configuration

```typescript
{
  httpOnly: true,        // Prevents XSS access
  secure: true,          // HTTPS only in production
  sameSite: 'Strict',    // CSRF protection
  maxAge: 30 days,       // 30-day expiration
  path: '/'              // Available site-wide
}
```

## API Changes

### Sign Up (`POST /v1/auth/signup`)
- **Before**: Returns `session.refresh_token` in response body
- **After**: Sets `refresh_token` cookie, removes from response body

### Sign In (`POST /v1/auth/signin`)
- **Before**: Returns `session.refreshToken` in response body  
- **After**: Sets `refresh_token` cookie, removes from response body

### Sign Out (`POST /v1/auth/signout`)
- **Before**: Required `refreshToken` in request body
- **After**: No request body needed, clears `refresh_token` cookie

### Refresh Token (`POST /v1/auth/refresh`)
- **Before**: Required `refreshToken` in request body
- **After**: Reads from `refresh_token` cookie, updates cookie with new token

## Files Modified

1. **`src/routes/v1/auth/auth.controller.ts`**
   - Added cookie imports and utilities
   - Updated all auth endpoints to use cookies
   - Removed refresh tokens from response bodies

2. **`src/routes/v1/auth/auth.routes.ts`**
   - Updated OpenAPI schemas to reflect cookie usage
   - Removed refresh token from response schemas
   - Updated endpoint descriptions

3. **`src/lib/cookie-utils.ts`** (new file)
   - Centralized cookie configuration
   - Environment-aware security settings
   - Reusable cookie options

## Client Integration

### Before (Token-based)
```javascript
// Sign in
const response = await fetch('/v1/auth/signin', {
  method: 'POST',
  body: JSON.stringify({ email, password })
});
const { session } = await response.json();
localStorage.setItem('refreshToken', session.refreshToken);

// Refresh
const refreshToken = localStorage.getItem('refreshToken');
await fetch('/v1/auth/refresh', {
  method: 'POST',
  body: JSON.stringify({ refreshToken })
});
```

### After (Cookie-based)
```javascript
// Sign in - cookies handled automatically
const response = await fetch('/v1/auth/signin', {
  method: 'POST',
  credentials: 'include', // Important: include cookies
  body: JSON.stringify({ email, password })
});

// Refresh - no token management needed
await fetch('/v1/auth/refresh', {
  method: 'POST',
  credentials: 'include' // Important: include cookies
});
```

## Security Benefits

1. **XSS Protection**: httpOnly cookies cannot be accessed by JavaScript
2. **CSRF Protection**: SameSite=Strict prevents cross-site requests
3. **Transport Security**: secure=true ensures HTTPS-only transmission
4. **Automatic Management**: Browser handles cookie storage/transmission
5. **Reduced Attack Surface**: No client-side token storage needed

## Migration Notes

- Existing clients must update to use `credentials: 'include'`
- Remove client-side refresh token storage logic
- Update refresh endpoint calls to not send request body
- Test cookie behavior across different domains/subdomains

## Testing

A test script is available at `test-cookie-auth.ts` to verify the cookie implementation works correctly.

## Environment Considerations

- **Development**: `secure: false` (allows HTTP testing)
- **Production**: `secure: true` (HTTPS required)
- **Cross-domain**: May need SameSite adjustments for subdomain usage