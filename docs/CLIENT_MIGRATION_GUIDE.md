# Client-Side Migration Guide: Better Auth Session System

## Overview

The authentication system has been migrated from a dual-token approach to Better Auth's session-based system using secure HTTP-only cookies. This simplifies client-side authentication and improves security.

## Key Changes

### Before (Token-Based)
```javascript
// Manual token management
const response = await fetch('/v1/auth/sign-in/email', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { token, user } = await response.json();
localStorage.setItem('authToken', token);

// Manual token usage
const apiResponse = await fetch('/v1/listings', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### After (Cookie-Based)
```javascript
// Automatic session management
const response = await fetch('/v1/auth/sign-in/email', {
  method: 'POST',
  credentials: 'include', // Important: Include cookies
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const { user } = await response.json(); // No token in response

// Automatic authentication via cookies
const apiResponse = await fetch('/v1/listings', {
  credentials: 'include' // Important: Include cookies
});
```

## Required Client Changes

### 1. Update All Fetch Requests

**Add `credentials: 'include'` to ALL API requests:**

```javascript
// ✅ Correct
fetch('/api/endpoint', {
  credentials: 'include',
  // ... other options
});

// ❌ Incorrect (cookies won't be sent)
fetch('/api/endpoint', {
  // ... other options
});
```

### 2. Remove Token Management Code

**Remove these patterns:**

```javascript
// ❌ Remove token storage
localStorage.setItem('authToken', token);
localStorage.getItem('authToken');
localStorage.removeItem('authToken');

// ❌ Remove Authorization headers
headers: { 'Authorization': `Bearer ${token}` }

// ❌ Remove token refresh logic
async function refreshToken() { /* ... */ }
```

### 3. Update Authentication Response Handling

**Before:**
```javascript
const { token, user, session, organization, profile } = await response.json();
```

**After:**
```javascript
const { user, session, organization, profile } = await response.json();
// No token field in response
```

### 4. Update API Client Configuration

If using the generated API client:

```javascript
// ✅ Automatic cookie handling (already configured)
const client = new ApiClient({
  baseUrl: 'http://localhost:3001'
});

// ❌ Remove manual token methods
// client.setAuth(token);
// client.clearAuth();
```

## Framework-Specific Examples

### React/Next.js

```javascript
// hooks/useAuth.js
export function useAuth() {
  const signIn = async (email, password) => {
    const response = await fetch('/v1/auth/sign-in/email', {
      method: 'POST',
      credentials: 'include', // Essential for cookies
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    
    if (response.ok) {
      const { user } = await response.json();
      return user;
    }
    throw new Error('Sign in failed');
  };

  const signOut = async () => {
    await fetch('/api/auth/sign-out', {
      method: 'POST',
      credentials: 'include' // Clears session cookie
    });
  };

  return { signIn, signOut };
}

// API calls
const fetchUserData = async () => {
  const response = await fetch('/v1/profiles/me', {
    credentials: 'include' // Include session cookie
  });
  return response.json();
};
```

### Vue.js

```javascript
// composables/useAuth.js
export function useAuth() {
  const signIn = async (email, password) => {
    const response = await $fetch('/v1/auth/sign-in/email', {
      method: 'POST',
      credentials: 'include',
      body: { email, password }
    });
    return response.user;
  };

  return { signIn };
}
```

### Angular

```typescript
// auth.service.ts
@Injectable()
export class AuthService {
  private http = inject(HttpClient);

  signIn(email: string, password: string) {
    return this.http.post('/v1/auth/sign-in/email', 
      { email, password },
      { withCredentials: true } // Angular equivalent of credentials: 'include'
    );
  }

  getProfile() {
    return this.http.get('/v1/profiles/me', {
      withCredentials: true
    });
  }
}
```

## CORS Configuration

Ensure your client's origin is included in the server's CORS configuration:

```javascript
// Server-side (already configured)
trustedOrigins: ['http://localhost:3000', 'https://yourdomain.com']
```

## Security Benefits

1. **XSS Protection**: HTTP-only cookies can't be accessed by JavaScript
2. **CSRF Protection**: SameSite cookie attributes prevent cross-site attacks
3. **Automatic Management**: No manual token handling reduces security risks
4. **Secure Transmission**: Cookies only sent over HTTPS in production

## Testing Authentication

```javascript
// Check if user is authenticated
const checkAuth = async () => {
  try {
    const response = await fetch('/api/auth/get-session', {
      credentials: 'include'
    });
    
    if (response.ok) {
      const { user } = await response.json();
      return user;
    }
    return null;
  } catch {
    return null;
  }
};
```

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Ensure `credentials: 'include'` is set
2. **CORS Errors**: Verify client origin is in server's trusted origins
3. **Session Not Persisting**: Check cookie settings and HTTPS in production

### Debug Session Cookies

```javascript
// Check if session cookie exists (for debugging only)
console.log('Cookies:', document.cookie);
// Look for 'better-auth.session_token' cookie
```

## Migration Checklist

- [ ] Add `credentials: 'include'` to all API requests
- [ ] Remove token storage (localStorage/sessionStorage)
- [ ] Remove Authorization headers
- [ ] Update auth response handling (no token field)
- [ ] Remove token refresh logic
- [ ] Test sign-in/sign-out flow
- [ ] Test protected API endpoints
- [ ] Verify session persistence across browser refreshes
