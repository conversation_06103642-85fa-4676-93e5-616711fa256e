### Better Auth: Server Usage (LLM‑ready)

This is a concise, comprehensive reference for using Better Auth on the server. It consolidates core usage, hooks, middleware, plugins, rate limits, sessions, users/accounts, admin, and organizations.

#### Install & Initialize

```ts
import { betterAuth } from "better-auth";

export const auth = betterAuth({
  // Email/password
  emailAndPassword: { enabled: true, autoSignIn: true },

  // Social providers (example: GitHub)
  socialProviders: {
    github: {
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    },
  },

  // Sessions
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24,     // refresh daily when used
    freshAge: 60 * 60 * 24,      // 1 day freshness window
    // cookieCache: { enabled: true, maxAge: 5 * 60 }, // optional
  },

  // Rate limit (prod defaults: 60s window, 100 max)
  rateLimit: {
    enabled: true,
    window: 60,
    max: 100,
    customRules: {
      "/sign-in/email": { window: 10, max: 3 },
    },
    // storage: "database" | "secondary-storage" | customStorage
  },

  // Advanced (IP header selection)
  advanced: {
    ipAddress: { ipAddressHeaders: ["x-forwarded-for"] },
  },
});
```

#### Server API Calls

Use `auth.api` with headers to run server-side flows and receive a Response when `asResponse: true`.

```ts
// Sign in (server)
const signInRes = await auth.api.signInEmail({
  body: { email, password },
  asResponse: true,
});

// Get session (server)
const session = await auth.api.getSession({ headers: req.headers });

// Set password (server-only)
await auth.api.setPassword({ body: { newPassword }, headers: req.headers });
```

### Hooks (createAuthMiddleware)

- before: run prior to endpoint execution (validate, rewrite body, early return)
- after: run after endpoint execution (augment response, side effects)

```ts
import { createAuthMiddleware, APIError } from "better-auth/api";

export const auth = betterAuth({
  hooks: {
    before: createAuthMiddleware(async (ctx) => {
      if (ctx.path === "/sign-up/email" && !ctx.body?.email.endsWith("@example.com")) {
        throw new APIError("BAD_REQUEST", { message: "Email must end with @example.com" });
      }
    }),
    after: createAuthMiddleware(async (ctx) => {
      if (ctx.path.startsWith("/sign-up") && ctx.context.newSession) {
        // notify, audit log, etc.
      }
    }),
  },
});
```

Hook utilities: `ctx.json`, `ctx.redirect`, `ctx.setCookies`/`setSignedCookie`, `ctx.getCookies`/`getSignedCookies`, throw `APIError`.

### Middleware (request-time, api-only)

Use plugin middlewares to run only for API requests. Throw `APIError` or return `Response` to stop the chain.

### Custom Endpoints (Plugins)

```ts
import { createAuthEndpoint } from "better-auth/api";

const myPlugin = () => ({
  id: "my-plugin",
  endpoints: {
    hello: createAuthEndpoint("/my-plugin/hello-world", { method: "GET" }, async (ctx) => {
      return ctx.json({ message: "Hello World" });
    }),
  },
});
```

`ctx.context` includes: appName, options, tables, baseURL, session config, secret, default cookies, `db`, `adapter`, `internalAdapter`, `createAuthCookie`.

### Database Hooks (server)

Intercept create/update operations to modify/police data.

```ts
export const auth = betterAuth({
  databaseHooks: {
    account: {
      create: {
        before(account) {
          // encrypt tokens before persist
          return { data: account };
        },
      },
    },
  },
});
```

### Sessions (server)

- Stored in DB; cookie is token. `expiresIn`, `updateAge`, `freshAge` control lifecycle
- `disableSessionRefresh: true` to skip rolling expiry
- Cookie cache optional for read performance (`session.cookieCache`)

Server examples:

```ts
// Force DB read (ignore cookie cache)
await auth.api.getSession({ headers: req.headers, query: { disableCookieCache: true } });

// Revoke sessions (server)
await auth.api.revokeOtherSessions({ headers: req.headers });
```

### Rate Limit

- Defaults (prod): 60s window, 100 requests
- Sensitive routes include stricter built-ins (e.g., `/sign-in/email`)
- Storage: in-memory (default), database, secondary-storage, or custom
- Exposes `X-Retry-After` header on 429

### Users & Accounts (server)

- Update user, change email, delete user (enable `user.deleteUser.enabled`)
- Passwords live on `account` table; use `changePassword` or server-only `setPassword`
- Account linking/unlinking; optional forced linking for trusted providers

Server example snippets:

```ts
// Delete user with verification hooks
export const auth = betterAuth({
  user: {
    deleteUser: {
      enabled: true,
      beforeDelete: async (user) => {
        // checks/cleanup
      },
      afterDelete: async (user) => {},
    },
  },
});
```

### Admin Plugin (server)

Enable powerful admin actions and access control.

```ts
import { admin } from "better-auth/plugins";

export const auth = betterAuth({
  plugins: [
    admin({
      defaultRole: "user",
      adminRoles: ["admin"],
      adminUserIds: [],
      impersonationSessionDuration: 60 * 60, // 1h
      bannedUserMessage: "You are banned.",
      defaultBanReason: "No reason",
      defaultBanExpiresIn: undefined,
    }),
  ],
});

// Server-side permission checks
await auth.api.userHasPermission({
  body: { userId, permissions: { project: ["create"] } },
});
```

Admin schema adds: `user.role`, `user.banned`, `user.banReason`, `user.banExpires`, and `session.impersonatedBy`.

### Organization Plugin (server)

```ts
import { organization } from "better-auth/plugins";

export const auth = betterAuth({
  plugins: [
    organization({
      allowUserToCreateOrganization: true,
      creatorRole: "owner", // or "admin"
      organizationLimit: 5,
      membershipLimit: 100,
      invitationExpiresIn: 60 * 60 * 48,
      cancelPendingInvitationsOnReInvite: false,
      sendInvitationEmail: async (data) => {
        const url = `https://example.com/accept-invitation/${data.id}`;
        // send email with url
      },
      organizationDeletion: {
        disabled: false,
        beforeDelete: async () => {},
        afterDelete: async () => {},
      },
      teams: { enabled: false },
    }),
  ],
});

// Check permissions for active user (server)
await auth.api.hasPermission({
  headers: req.headers,
  body: { permissions: { project: ["create"] } },
});
```

Organization schema adds `organization`, `member`, `invitation`, optional `team`/`teamMember`, and in `session`: `activeOrganizationId`, `activeTeamId`.

### Common Server Patterns

- Enforce org-based access: run a before hook that checks active organization in session
- Multi-tenant rate limiting: add customRules by path prefix (e.g., `/organization/*`)
- Custom response shaping: use `customSession` plugin to add computed fields

```ts
import { customSession } from "better-auth/plugins";

export const auth = betterAuth({
  plugins: [
    customSession(async ({ user, session }) => {
      const roles = await getRoles(session.session.userId);
      return { roles, user, session };
    }),
  ],
});
```

### Error Handling

- Throw `APIError(code, { message })` in hooks/middleware/endpoints
- On 429, inspect `X-Retry-After`
- Prefer `asResponse: true` when you need to forward raw Response to HTTP frameworks

---

Use this doc as a compact reference for building server-side auth, permissions, and org management with Better Auth. For client usage, see the companion client guide.


