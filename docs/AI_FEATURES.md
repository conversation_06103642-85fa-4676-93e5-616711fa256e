# AI-Powered Listing Features

This document describes the AI-powered features available in the listings service that integrate with OpenAI's chat completion API.

## Setup

### Environment Variables

Add your OpenAI API key to your environment file:

```bash
# .env
OPENAI_API_KEY=your_openai_api_key_here
```

You can get an API key from [OpenAI's platform](https://platform.openai.com/api-keys).

### Dependencies

The OpenAI integration uses the official `openai` npm package, which is automatically installed.

## Available AI Features

### 1. Generate Listing Description

**Endpoint:** `POST /v1/listings/{listing_id}/ai/generate-description`

Automatically generates a professional business listing description based on the listing's data.

**Example Request:**
```bash
curl -X POST "http://localhost:3000/v1/listings/123e4567-e89b-12d3-a456-426614174000/ai/generate-description" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "description": "Established in 2015, this thriving downtown restaurant represents an exceptional opportunity in the food & beverage industry. With annual revenue of $500,000 and strong cash flow of $75,000, this popular local establishment has built a loyal customer base over 8 years of operation..."
  }
}
```

### 2. Analyze Listing Data

**Endpoint:** `POST /v1/listings/{listing_id}/ai/analyze`

Provides AI-powered analysis of the business listing including market positioning, valuation assessment, and recommendations.

**Example Request:**
```bash
curl -X POST "http://localhost:3000/v1/listings/123e4567-e89b-12d3-a456-426614174000/ai/analyze" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "analysis": "Market Positioning: This business appears well-positioned in the local market with strong fundamentals. Valuation Assessment: The asking price represents a reasonable 3.3x multiple of cash flow. Potential Risks: Consider seasonal variations and local competition. Opportunities: Strong potential for expansion and digital marketing improvements..."
  }
}
```

### 3. Generate Custom Insights

**Endpoint:** `POST /v1/listings/{listing_id}/ai/insights`

Generate custom AI insights with an optional custom prompt for specific analysis needs.

**Example Request:**
```bash
curl -X POST "http://localhost:3000/v1/listings/123e4567-e89b-12d3-a456-426614174000/ai/insights" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "custom_prompt": "Focus on growth opportunities and potential risks for this business in the current market"
  }'
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "insights": "Growth Opportunities: 1) Digital presence expansion through social media and online ordering, 2) Catering services development, 3) Extended hours operation. Potential Risks: 1) Rising food costs impacting margins, 2) Labor shortage in hospitality sector, 3) Economic downturn affecting discretionary spending..."
  }
}
```

### 4. Custom AI Chat Completion

**Endpoint:** `POST /v1/listings/ai/chat`

Direct access to OpenAI's chat completion with custom system prompts and user inputs.

**Example Request:**
```bash
curl -X POST "http://localhost:3000/v1/listings/ai/chat" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "system_prompt": "You are a business valuation expert. Provide detailed analysis.",
    "user_input": "Analyze a restaurant with $500K revenue and $75K cash flow asking $250K",
    "model": "gpt-3.5-turbo",
    "max_tokens": 1000,
    "temperature": 0.7
  }'
```

**Example Response:**
```json
{
  "success": true,
  "data": {
    "content": "Based on the financial metrics provided, this restaurant presents an interesting investment opportunity. The asking price of $250K represents a 3.33x multiple of the $75K cash flow, which is reasonable for the restaurant industry...",
    "model": "gpt-3.5-turbo",
    "usage": {
      "prompt_tokens": 45,
      "completion_tokens": 150,
      "total_tokens": 195
    }
  }
}
```

## Service Layer Usage

You can also use the AI features directly in your service layer:

```typescript
import { ListingsService } from '@/routes/v1/listings/listings.service';

// Generate description for a listing
const description = await ListingsService.generateListingDescription(listingId, workspaceId);

// Analyze listing data
const analysis = await ListingsService.analyzeListingData(listingId, workspaceId);

// Generate custom insights
const insights = await ListingsService.generateListingInsights(listingId, workspaceId, customPrompt);

// Custom AI completion
const response = await ListingsService.customAICompletion({
  systemPrompt: "You are a helpful assistant",
  userInput: "Analyze this business",
  model: "gpt-3.5-turbo",
  maxTokens: 500,
  temperature: 0.7
});
```

## Error Handling

All AI endpoints include proper error handling for:

- **404**: Listing not found
- **401**: Unauthorized access
- **403**: Insufficient permissions
- **500**: AI service errors or OpenAI API issues

## Rate Limiting & Costs

- OpenAI API calls are subject to your OpenAI account's rate limits and billing
- Consider implementing caching for frequently requested analyses
- Monitor your OpenAI usage through their dashboard

## Security Considerations

- API keys are stored securely in environment variables
- All endpoints require authentication
- User data is only sent to OpenAI as necessary for the requested analysis
- No sensitive data is logged in plain text
