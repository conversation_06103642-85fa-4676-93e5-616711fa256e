#!/usr/bin/env node

/**
 * Debug script to test authentication and cookie handling
 * Run with: node debug-auth.js
 */

const BASE_URL = process.env.API_URL || 'http://localhost:9999';

async function testAuth() {
  console.log('🔍 Testing Authentication Setup...\n');

  // Test 1: Check GET /get-session endpoint
  console.log('1. Testing GET /get-session endpoint:');
  try {
    const response = await fetch(`${BASE_URL}/get-session`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Headers:`, Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   Response:`, data);
    } else {
      const text = await response.text();
      console.log(`   Error:`, text);
    }
  } catch (error) {
    console.log(`   Error:`, error.message);
  }

  console.log('\n2. Testing Better Auth GET /api/auth/get-session:');
  try {
    const response = await fetch(`${BASE_URL}/api/auth/get-session`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log(`   Status: ${response.status}`);
    console.log(`   Headers:`, Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log(`   Response:`, data);
    } else {
      const text = await response.text();
      console.log(`   Error:`, text);
    }
  } catch (error) {
    console.log(`   Error:`, error.message);
  }

  console.log('\n3. Testing CORS preflight for listings API:');
  try {
    const response = await fetch(`${BASE_URL}/v1/listings`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3001',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type',
      }
    });
    
    console.log(`   Status: ${response.status}`);
    console.log(`   CORS Headers:`);
    console.log(`     Access-Control-Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin')}`);
    console.log(`     Access-Control-Allow-Credentials: ${response.headers.get('Access-Control-Allow-Credentials')}`);
    console.log(`     Access-Control-Allow-Methods: ${response.headers.get('Access-Control-Allow-Methods')}`);
    console.log(`     Access-Control-Allow-Headers: ${response.headers.get('Access-Control-Allow-Headers')}`);
  } catch (error) {
    console.log(`   Error:`, error.message);
  }

  console.log('\n4. Testing listings API without auth (should get 401):');
  try {
    const response = await fetch(`${BASE_URL}/v1/listings`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log(`   Status: ${response.status}`);
    if (!response.ok) {
      const data = await response.json();
      console.log(`   Error Response:`, data);
    }
  } catch (error) {
    console.log(`   Error:`, error.message);
  }

  console.log('\n📋 Summary:');
  console.log('   - GET /get-session should return 200 with { session: null, user: null } when not authenticated');
  console.log('   - CORS should allow credentials and have exact origin match');
  console.log('   - Listings API should return 401 when not authenticated');
  console.log('   - Cookie names should be: rendyr.session_token (not rendyr.session_data)');
  console.log('\n💡 Next steps:');
  console.log('   1. Sign in via POST /v1/auth/sign-in/email to get session cookies');
  console.log('   2. Test listings API again with valid session cookies');
  console.log('   3. Check browser dev tools for cookie names and values');
}

// Run the test
testAuth().catch(console.error);
