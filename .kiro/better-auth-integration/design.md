# Design Document

## Overview

This design outlines the implementation of a comprehensive authentication system for a React application using better-auth. The system will provide secure user authentication, session management, and user profile functionality with optional advanced features like social authentication, passkeys, 2FA, and multi-tenant support.

The architecture follows modern authentication best practices with secure session management, comprehensive error handling, and a flexible plugin system for optional features.

**Note**: Role-Based Access Control (RBAC) and admin functionality are considered optional features that can be implemented after the core authentication system is established.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (React)"
        A[React App] --> B[Auth Client]
        B --> C[Auth Context]
        C --> D[Protected Routes]
        C --> E[Auth Components]
        E --> F[Sign In Form]
        E --> G[Sign Up Form]
        E --> H[Profile Form]
        C --> S[Subscription Components]
        S --> T[Pricing Page]
        S --> U[Subscription Management]
        S --> V[Billing Portal]
    end

    subgraph "External Services"
        I[Better Auth Server]
        J[Social Providers]
        K[Stripe API]
        L[Stripe Checkout]
        M[Stripe Billing Portal]
    end

    B <--> I
    I <--> J
    I <--> K
    S <--> L
    S <--> M

    subgraph "Client-Side Features"
        N[Session Management]
        O[Error Handling]
        P[Form Validation]
        Q[Subscription State]
    end

    C --> N
    E --> O
    E --> P
    S --> Q
```

### Technology Stack

- **Frontend Framework**: React 18 with TypeScript
- **Authentication Library**: better-auth (client-side only)
- **Payment Processing**: Stripe (optional integration)
- **Social Providers**: Google OAuth
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Context + better-auth hooks
- **Routing**: TanStack Router
- **Auth Server**: External better-auth server (assumed to be running)

### API Reference

For complete API documentation and available endpoints, refer to the Better Auth OpenAPI specification at:
`{backend_url}/api/auth/reference`

This endpoint provides comprehensive documentation for all authentication, organization, and admin endpoints exposed by the Better Auth server, including request/response schemas, authentication requirements, and usage examples.

## Components and Interfaces

### Core Authentication Components

#### 1. Auth Client Configuration

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/react";
import { magicLinkClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    magicLinkClient(), // Enable magic link authentication
  ],
  fetchOptions: {
    onError: (ctx) => {
      // Global error handling
      console.error("Auth error:", ctx.error);
    },
    onSuccess: (ctx) => {
      // Global success handling
      console.log("Auth success:", ctx.response);
    },
  },
});

export const { 
  useSession, 
  useUser, 
  signIn, 
  signUp, 
  signOut,
  magicLink, // Magic link verification methods
} = authClient;

// Export error codes for custom error handling
export const AUTH_ERROR_CODES = authClient.$ERROR_CODES;
```

#### 2. Auth Context Provider

```typescript
// contexts/AuthContext.tsx
import { useSession, useUser } from "@/lib/auth-client";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  error: string | null;
  clearError: () => void;
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    data: session,
    isPending: sessionLoading,
    error: sessionError,
    refetch: refetchSession,
  } = useSession();

  const { data: user, isPending: userLoading, error: userError } = useUser();

  const [globalError, setGlobalError] = useState<string | null>(null);

  const loading = sessionLoading || userLoading;
  const error = sessionError?.message || userError?.message || globalError;

  const clearError = useCallback(() => {
    setGlobalError(null);
  }, []);

  const value: AuthContextType = {
    user,
    session,
    loading,
    error,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

#### 3. Protected Route Component (TanStack Router)

```typescript
// components/ProtectedRoute.tsx
import { useSession } from "@/lib/auth-client";
import { redirect } from "@tanstack/react-router";

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAuth?: boolean; // false in development, true in production
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  fallback = <div>Please sign in to access this page</div>,
  requireAuth = process.env.NODE_ENV === "production",
}) => {
  const { data: session, isPending } = useSession();

  // In development, skip auth check if requireAuth is false
  if (!requireAuth) {
    return <>{children}</>;
  }

  if (isPending) {
    return <div>Loading...</div>;
  }

  if (!session) {
    return fallback;
  }

  return <>{children}</>;
};

// Route-level protection using TanStack Router
export const createProtectedRoute = (routeConfig: any) => ({
  ...routeConfig,
  beforeLoad: ({ context }: any) => {
    const requireAuth = process.env.NODE_ENV === "production";

    if (requireAuth && !context.session) {
      throw redirect({
        to: "/sign-in",
        search: {
          redirect: window.location.pathname,
        },
      });
    }
  },
});
```

### Better Auth Hooks Usage

#### Available Hooks

```typescript
// All hooks available from better-auth/react
import {
  useSession, // Get current session data
  useUser, // Get current user data
  useListSessions, // List all user sessions
} from "@/lib/auth-client";

// Hook usage examples
export const SessionExample = () => {
  const {
    data: session, // Session object or null
    isPending, // Loading state
    error, // Error object if any
    refetch, // Function to refetch session
  } = useSession();

  if (isPending) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!session) return <div>Not authenticated</div>;

  return <div>Welcome, {session.user.name}!</div>;
};

export const UserExample = () => {
  const {
    data: user, // User object or null
    isPending, // Loading state
    error, // Error object if any
  } = useUser();

  return (
    <div>
      {user ? (
        <div>
          <h1>Hello, {user.name}</h1>
          <p>Email: {user.email}</p>
          <p>Verified: {user.emailVerified ? "Yes" : "No"}</p>
        </div>
      ) : (
        <div>Please sign in</div>
      )}
    </div>
  );
};
```

### Authentication Forms

#### 1. Sign In Form

```typescript
// components/auth/SignInForm.tsx
import { authClient } from "@/lib/auth-client";

interface SignInFormData {
  email: string;
  password: string;
}

export const SignInForm: React.FC = () => {
  const [formData, setFormData] = useState<SignInFormData>({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showMagicLink, setShowMagicLink] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const { data, error: authError } = await authClient.signIn.email(
      {
        email: formData.email,
        password: formData.password,
      },
      {
        onError: (ctx) => {
          setError(ctx.error.message);
        },
        onSuccess: () => {
          // Redirect to dashboard
          window.location.href = "/dashboard";
        },
      }
    );

    setIsLoading(false);
  };

  const handleMagicLinkSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const { data, error: authError } = await authClient.signIn.magicLink(
      {
        email: formData.email,
        callbackURL: "/dashboard",
        newUserCallbackURL: "/welcome",
        errorCallbackURL: "/sign-in?error=magic-link-failed",
      },
      {
        onError: (ctx) => {
          setError(ctx.error.message);
        },
        onSuccess: () => {
          // Show success message
          setError(null);
          // Could show a success message instead
        },
      }
    );

    setIsLoading(false);
  };

  // Form JSX implementation with toggle between password and magic link
};
```

#### 2. Sign Up Form

```typescript
// components/auth/SignUpForm.tsx
import { authClient } from "@/lib/auth-client";

interface SignUpFormData {
  email: string;
  password: string;
  confirmPassword: string;
  name?: string;
}

export const SignUpForm: React.FC = () => {
  const [formData, setFormData] = useState<SignUpFormData>({
    email: "",
    password: "",
    confirmPassword: "",
    name: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    const { data, error: authError } = await authClient.signUp.email(
      {
        email: formData.email,
        password: formData.password,
        name: formData.name,
      },
      {
        onError: (ctx) => {
          setError(ctx.error.message);
        },
        onSuccess: () => {
          // Redirect to dashboard or email verification page
          window.location.href = "/dashboard";
        },
      }
    );

    setIsLoading(false);
  };

  // Form JSX implementation
};
```

#### 3. Magic Link Form Component

```typescript
// components/auth/MagicLinkForm.tsx
import { authClient } from "@/lib/auth-client";

interface MagicLinkFormData {
  email: string;
  name?: string; // For new user registration
}

export const MagicLinkForm: React.FC = () => {
  const [formData, setFormData] = useState<MagicLinkFormData>({
    email: "",
    name: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    const { data, error: authError } = await authClient.signIn.magicLink(
      {
        email: formData.email,
        name: formData.name, // Optional for new users
        callbackURL: "/dashboard",
        newUserCallbackURL: "/welcome",
        errorCallbackURL: "/sign-in?error=magic-link-failed",
      },
      {
        onError: (ctx) => {
          setError(ctx.error.message);
        },
        onSuccess: () => {
          setSuccess(true);
          setError(null);
        },
      }
    );

    setIsLoading(false);
  };

  if (success) {
    return (
      <div className="text-center p-6">
        <h2 className="text-xl font-semibold mb-2">Check your email</h2>
        <p className="text-gray-600 mb-4">
          We've sent a magic link to {formData.email}. Click the link in the email to sign in.
        </p>
        <button
          onClick={() => setSuccess(false)}
          className="text-blue-600 hover:underline"
        >
          Try a different email
        </button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-1">
          Email address
        </label>
        <input
          id="email"
          type="email"
          required
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          className="w-full p-2 border rounded-md"
          placeholder="Enter your email"
        />
      </div>

      <div>
        <label htmlFor="name" className="block text-sm font-medium mb-1">
          Name (optional, for new accounts)
        </label>
        <input
          id="name"
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full p-2 border rounded-md"
          placeholder="Enter your name"
        />
      </div>

      {error && (
        <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? "Sending..." : "Send Magic Link"}
      </button>
    </form>
  );
};
```

#### 4. Magic Link Verification Handler

```typescript
// components/auth/MagicLinkVerification.tsx
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { authClient } from "@/lib/auth-client";

export const MagicLinkVerification: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<"verifying" | "success" | "error">("verifying");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const token = searchParams.get("token");
    const callbackURL = searchParams.get("callbackURL");

    if (!token) {
      setStatus("error");
      setError("Invalid magic link - missing token");
      return;
    }

    const verifyMagicLink = async () => {
      try {
        const { data, error: verifyError } = await authClient.magicLink.verify({
          token,
          callbackURL: callbackURL || "/dashboard",
        });

        if (verifyError) {
          setStatus("error");
          setError(verifyError.message);
        } else {
          setStatus("success");
          // Redirect will be handled by the server response
        }
      } catch (err: any) {
        setStatus("error");
        setError(err.message || "Failed to verify magic link");
      }
    };

    verifyMagicLink();
  }, [searchParams]);

  if (status === "verifying") {
    return (
      <div className="text-center p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold mb-2">Verifying your magic link...</h2>
        <p className="text-gray-600">Please wait while we sign you in.</p>
      </div>
    );
  }

  if (status === "error") {
    return (
      <div className="text-center p-6">
        <h2 className="text-xl font-semibold mb-2 text-red-600">Verification Failed</h2>
        <p className="text-gray-600 mb-4">{error}</p>
        <a
          href="/sign-in"
          className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Back to Sign In
        </a>
      </div>
    );
  }

  return (
    <div className="text-center p-6">
      <h2 className="text-xl font-semibold mb-2 text-green-600">Success!</h2>
      <p className="text-gray-600 mb-4">You have been successfully signed in.</p>
      <p className="text-sm text-gray-500">Redirecting...</p>
    </div>
  );
};
```

#### 5. Password Reset Forms

```typescript
// components/auth/ForgotPasswordForm.tsx
// components/auth/ResetPasswordForm.tsx
```

### User Profile Components

#### 1. Profile View/Edit Component

```typescript
// components/profile/ProfileForm.tsx
interface ProfileFormData {
  name: string;
  email: string;
  avatar?: File;
}

export const ProfileForm: React.FC = () => {
  // Profile management implementation
};
```

### Stripe Integration Components (Optional Feature)

#### 1. Stripe Client Configuration

```typescript
// lib/stripe-client.ts
import { authClient } from "@/lib/auth-client";

// Extend the auth client with Stripe functionality when enabled
export const stripeClient = authClient.subscription ? {
  // Subscription management methods
  upgrade: authClient.subscription.upgrade,
  cancel: authClient.subscription.cancel,
  restore: authClient.subscription.restore,
  list: authClient.subscription.list,
  
  // Hooks for subscription data
  useSubscriptions: authClient.useSubscriptions,
  useActiveSubscription: authClient.useActiveSubscription,
} : null;

// Type definitions for subscription data
export interface SubscriptionPlan {
  name: string;
  priceId: string;
  annualDiscountPriceId?: string;
  limits: Record<string, number>;
  freeTrial?: {
    days: number;
  };
}

export interface UserSubscription {
  id: string;
  plan: string;
  referenceId: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  status: 'active' | 'canceled' | 'trialing' | 'incomplete' | 'past_due';
  periodStart?: Date;
  periodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
  seats?: number;
  trialStart?: Date;
  trialEnd?: Date;
  limits?: Record<string, number>;
}
```

#### 2. Pricing Page Component

```typescript
// components/subscription/PricingPage.tsx
import { stripeClient } from "@/lib/stripe-client";

interface PricingPageProps {
  plans: SubscriptionPlan[];
  currentPlan?: string;
  showAnnualToggle?: boolean;
}

export const PricingPage: React.FC<PricingPageProps> = ({
  plans,
  currentPlan,
  showAnnualToggle = true,
}) => {
  const [isAnnual, setIsAnnual] = useState(false);
  const [loading, setLoading] = useState<string | null>(null);
  const { data: subscriptions } = stripeClient?.useSubscriptions() || { data: [] };
  
  const activeSubscription = subscriptions?.find(
    sub => sub.status === 'active' || sub.status === 'trialing'
  );

  const handleUpgrade = async (planName: string) => {
    if (!stripeClient) return;
    
    setLoading(planName);
    
    try {
      const { error } = await stripeClient.upgrade({
        plan: planName,
        annual: isAnnual,
        subscriptionId: activeSubscription?.stripeSubscriptionId,
        successUrl: `${window.location.origin}/billing/success`,
        cancelUrl: `${window.location.origin}/pricing`,
        seats: 1, // Default to 1 seat, can be customized
      });

      if (error) {
        console.error('Subscription upgrade failed:', error);
        // Handle error (show toast, etc.)
      }
    } catch (err) {
      console.error('Subscription upgrade error:', err);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="pricing-page">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-4">Choose Your Plan</h1>
        <p className="text-gray-600 mb-6">
          Select the perfect plan for your needs
        </p>
        
        {showAnnualToggle && (
          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={!isAnnual ? 'font-semibold' : 'text-gray-500'}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                isAnnual ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={isAnnual ? 'font-semibold' : 'text-gray-500'}>
              Annual
              <span className="ml-1 text-sm text-green-600">(Save 20%)</span>
            </span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {plans.map((plan) => {
          const isCurrentPlan = activeSubscription?.plan === plan.name;
          const isTrialing = activeSubscription?.status === 'trialing' && isCurrentPlan;
          
          return (
            <div
              key={plan.name}
              className={`border rounded-lg p-6 ${
                isCurrentPlan ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`}
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold capitalize mb-2">
                  {plan.name}
                </h3>
                
                {isCurrentPlan && (
                  <div className="mb-2">
                    <span className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded">
                      {isTrialing ? 'Free Trial' : 'Current Plan'}
                    </span>
                  </div>
                )}
                
                <div className="text-3xl font-bold mb-1">
                  ${isAnnual ? '99' : '10'}
                  <span className="text-lg font-normal text-gray-500">
                    /{isAnnual ? 'year' : 'month'}
                  </span>
                </div>
                
                {plan.freeTrial && (
                  <p className="text-sm text-green-600">
                    {plan.freeTrial.days}-day free trial
                  </p>
                )}
              </div>

              <div className="mb-6">
                <h4 className="font-semibold mb-3">Features:</h4>
                <ul className="space-y-2 text-sm">
                  {Object.entries(plan.limits).map(([feature, limit]) => (
                    <li key={feature} className="flex items-center">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {limit === -1 ? `Unlimited ${feature}` : `${limit} ${feature}`}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={() => handleUpgrade(plan.name)}
                disabled={loading === plan.name || isCurrentPlan}
                className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
                  isCurrentPlan
                    ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                    : loading === plan.name
                    ? 'bg-blue-400 text-white cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {loading === plan.name
                  ? 'Processing...'
                  : isCurrentPlan
                  ? 'Current Plan'
                  : `Upgrade to ${plan.name}`}
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

#### 3. Subscription Management Component

```typescript
// components/subscription/SubscriptionManagement.tsx
import { stripeClient } from "@/lib/stripe-client";

export const SubscriptionManagement: React.FC = () => {
  const { data: subscriptions, isLoading } = stripeClient?.useSubscriptions() || { 
    data: [], 
    isLoading: false 
  };
  
  const [cancelLoading, setCancelLoading] = useState(false);
  const [restoreLoading, setRestoreLoading] = useState(false);

  const activeSubscription = subscriptions?.find(
    sub => sub.status === 'active' || sub.status === 'trialing'
  );

  const handleCancelSubscription = async () => {
    if (!stripeClient || !activeSubscription) return;

    setCancelLoading(true);
    
    try {
      const { error } = await stripeClient.cancel({
        subscriptionId: activeSubscription.stripeSubscriptionId!,
        returnUrl: `${window.location.origin}/billing`,
      });

      if (error) {
        console.error('Subscription cancellation failed:', error);
      }
    } catch (err) {
      console.error('Subscription cancellation error:', err);
    } finally {
      setCancelLoading(false);
    }
  };

  const handleRestoreSubscription = async () => {
    if (!stripeClient || !activeSubscription) return;

    setRestoreLoading(true);
    
    try {
      const { error } = await stripeClient.restore({
        subscriptionId: activeSubscription.stripeSubscriptionId!,
      });

      if (error) {
        console.error('Subscription restoration failed:', error);
      } else {
        // Refresh subscription data
        window.location.reload();
      }
    } catch (err) {
      console.error('Subscription restoration error:', err);
    } finally {
      setRestoreLoading(false);
    }
  };

  if (!stripeClient) {
    return (
      <div className="text-center p-6">
        <p className="text-gray-500">Subscription management is not available.</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="text-center p-6">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-500">Loading subscription...</p>
      </div>
    );
  }

  if (!activeSubscription) {
    return (
      <div className="text-center p-6">
        <h2 className="text-xl font-semibold mb-4">No Active Subscription</h2>
        <p className="text-gray-600 mb-4">
          You don't have an active subscription. Choose a plan to get started.
        </p>
        <a
          href="/pricing"
          className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          View Plans
        </a>
      </div>
    );
  }

  const isTrialing = activeSubscription.status === 'trialing';
  const isCanceling = activeSubscription.cancelAtPeriodEnd;

  return (
    <div className="subscription-management max-w-2xl mx-auto">
      <div className="bg-white border rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Subscription Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-500 mb-1">
              Current Plan
            </label>
            <p className="text-lg font-semibold capitalize">
              {activeSubscription.plan}
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-500 mb-1">
              Status
            </label>
            <span className={`inline-block px-2 py-1 rounded text-sm font-medium ${
              isTrialing
                ? 'bg-green-100 text-green-800'
                : isCanceling
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-blue-100 text-blue-800'
            }`}>
              {isTrialing ? 'Free Trial' : isCanceling ? 'Canceling' : 'Active'}
            </span>
          </div>
          
          {activeSubscription.periodEnd && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                {isCanceling ? 'Ends On' : 'Next Billing'}
              </label>
              <p className="text-lg">
                {new Date(activeSubscription.periodEnd).toLocaleDateString()}
              </p>
            </div>
          )}
          
          {activeSubscription.seats && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-1">
                Seats
              </label>
              <p className="text-lg">{activeSubscription.seats}</p>
            </div>
          )}
        </div>

        {activeSubscription.limits && (
          <div className="mb-6">
            <h3 className="font-semibold mb-3">Plan Limits</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(activeSubscription.limits).map(([feature, limit]) => (
                <div key={feature} className="text-center p-3 bg-gray-50 rounded">
                  <p className="text-2xl font-bold text-blue-600">
                    {limit === -1 ? '∞' : limit}
                  </p>
                  <p className="text-sm text-gray-600 capitalize">{feature}</p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3">
          <a
            href="/pricing"
            className="flex-1 text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Change Plan
          </a>
          
          {isCanceling ? (
            <button
              onClick={handleRestoreSubscription}
              disabled={restoreLoading}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {restoreLoading ? 'Restoring...' : 'Restore Subscription'}
            </button>
          ) : (
            <button
              onClick={handleCancelSubscription}
              disabled={cancelLoading}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {cancelLoading ? 'Processing...' : 'Cancel Subscription'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
```

#### 4. Subscription Context Provider

```typescript
// contexts/SubscriptionContext.tsx
import { stripeClient } from "@/lib/stripe-client";

interface SubscriptionContextType {
  subscriptions: UserSubscription[];
  activeSubscription: UserSubscription | null;
  loading: boolean;
  error: string | null;
  hasFeature: (feature: string) => boolean;
  getLimit: (feature: string) => number;
  isWithinLimit: (feature: string, currentUsage: number) => boolean;
}

export const SubscriptionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { data: subscriptions = [], isLoading, error } = stripeClient?.useSubscriptions() || {
    data: [],
    isLoading: false,
    error: null,
  };

  const activeSubscription = subscriptions.find(
    sub => sub.status === 'active' || sub.status === 'trialing'
  ) || null;

  const hasFeature = useCallback((feature: string) => {
    if (!activeSubscription?.limits) return false;
    return feature in activeSubscription.limits;
  }, [activeSubscription]);

  const getLimit = useCallback((feature: string) => {
    if (!activeSubscription?.limits) return 0;
    return activeSubscription.limits[feature] || 0;
  }, [activeSubscription]);

  const isWithinLimit = useCallback((feature: string, currentUsage: number) => {
    const limit = getLimit(feature);
    return limit === -1 || currentUsage < limit; // -1 means unlimited
  }, [getLimit]);

  const value: SubscriptionContextType = {
    subscriptions,
    activeSubscription,
    loading: isLoading,
    error: error?.message || null,
    hasFeature,
    getLimit,
    isWithinLimit,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
```

#### 5. Feature Gate Component

```typescript
// components/subscription/FeatureGate.tsx
import { useSubscription } from "@/contexts/SubscriptionContext";

interface FeatureGateProps {
  feature: string;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

export const FeatureGate: React.FC<FeatureGateProps> = ({
  feature,
  fallback = <div>This feature requires a subscription.</div>,
  children,
}) => {
  const { hasFeature, activeSubscription } = useSubscription();

  if (!activeSubscription) {
    return (
      <div className="text-center p-6 border rounded-lg">
        <h3 className="text-lg font-semibold mb-2">Subscription Required</h3>
        <p className="text-gray-600 mb-4">
          You need an active subscription to access this feature.
        </p>
        <a
          href="/pricing"
          className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          View Plans
        </a>
      </div>
    );
  }

  if (!hasFeature(feature)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
```

#### 6. Usage Limit Component

```typescript
// components/subscription/UsageLimitIndicator.tsx
import { useSubscription } from "@/contexts/SubscriptionContext";

interface UsageLimitIndicatorProps {
  feature: string;
  currentUsage: number;
  showUpgradePrompt?: boolean;
}

export const UsageLimitIndicator: React.FC<UsageLimitIndicatorProps> = ({
  feature,
  currentUsage,
  showUpgradePrompt = true,
}) => {
  const { getLimit, isWithinLimit } = useSubscription();
  
  const limit = getLimit(feature);
  const withinLimit = isWithinLimit(feature, currentUsage);
  const isUnlimited = limit === -1;
  
  if (isUnlimited) {
    return (
      <div className="text-sm text-gray-500">
        {currentUsage} / Unlimited {feature}
      </div>
    );
  }

  const percentage = (currentUsage / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = currentUsage >= limit;

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="text-gray-600 capitalize">{feature} Usage</span>
        <span className={`font-medium ${
          isAtLimit ? 'text-red-600' : isNearLimit ? 'text-yellow-600' : 'text-gray-900'
        }`}>
          {currentUsage} / {limit}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all ${
            isAtLimit ? 'bg-red-500' : isNearLimit ? 'bg-yellow-500' : 'bg-blue-500'
          }`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
      
      {isAtLimit && showUpgradePrompt && (
        <div className="text-sm text-red-600">
          You've reached your {feature} limit.{' '}
          <a href="/pricing" className="underline hover:no-underline">
            Upgrade your plan
          </a>{' '}
          to continue.
        </div>
      )}
    </div>
  );
};
```

### Admin Components (Optional Features)

**Note**: The following admin components are optional and can be implemented after the core authentication system is complete.

#### 1. Admin Dashboard Component

```typescript
// components/admin/AdminDashboard.tsx
import { useAdminPermissions } from "@/lib/auth-client";

export const AdminDashboard: React.FC = () => {
  const { isAdmin } = useAdminPermissions();

  if (!isAdmin) {
    return <div>Access denied. Admin privileges required.</div>;
  }

  return (
    <div className="admin-dashboard">
      <h1>Admin Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <AdminUserManagement />
        <AdminSessionManagement />
        <AdminRoleManagement />
      </div>
    </div>
  );
};
```

#### 2. User Management Component

```typescript
// components/admin/AdminUserManagement.tsx
interface AdminUser {
  id: string;
  email: string;
  name?: string;
  role: string | string[];
  banned: boolean;
  banReason?: string;
  banExpires?: Date;
  createdAt: Date;
}

export const AdminUserManagement: React.FC = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 10;

  const fetchUsers = async (page: number = 1, search: string = "") => {
    setLoading(true);
    try {
      const { data } = await authClient.admin.listUsers({
        query: {
          limit: pageSize,
          offset: (page - 1) * pageSize,
          searchValue: search,
          searchField: "email",
          searchOperator: "contains",
          sortBy: "createdAt",
          sortDirection: "desc",
        },
      });

      setUsers(data.users);
      setTotalPages(Math.ceil(data.total / pageSize));
    } catch (error) {
      console.error("Failed to fetch users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleBanUser = async (userId: string, reason?: string) => {
    try {
      await authClient.admin.banUser({
        userId,
        banReason: reason || "Banned by admin",
        banExpiresIn: 60 * 60 * 24 * 7, // 7 days
      });
      fetchUsers(currentPage, searchQuery);
    } catch (error) {
      console.error("Failed to ban user:", error);
    }
  };

  const handleUnbanUser = async (userId: string) => {
    try {
      await authClient.admin.unbanUser({ userId });
      fetchUsers(currentPage, searchQuery);
    } catch (error) {
      console.error("Failed to unban user:", error);
    }
  };

  const handleSetUserRole = async (userId: string, role: string | string[]) => {
    try {
      await authClient.admin.setRole({ userId, role });
      fetchUsers(currentPage, searchQuery);
    } catch (error) {
      console.error("Failed to set user role:", error);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this user? This action cannot be undone."
      )
    ) {
      return;
    }

    try {
      await authClient.admin.removeUser({ userId });
      fetchUsers(currentPage, searchQuery);
    } catch (error) {
      console.error("Failed to delete user:", error);
    }
  };

  useEffect(() => {
    fetchUsers(currentPage, searchQuery);
  }, [currentPage, searchQuery]);

  return (
    <div className="user-management">
      <div className="flex justify-between items-center mb-4">
        <h2>User Management</h2>
        <CreateUserModal
          onUserCreated={() => fetchUsers(currentPage, searchQuery)}
        />
      </div>

      <div className="mb-4">
        <input
          type="text"
          placeholder="Search users by email..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full p-2 border rounded"
        />
      </div>

      {loading ? (
        <div>Loading users...</div>
      ) : (
        <>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border p-2 text-left">Email</th>
                  <th className="border p-2 text-left">Name</th>
                  <th className="border p-2 text-left">Role</th>
                  <th className="border p-2 text-left">Status</th>
                  <th className="border p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td className="border p-2">{user.email}</td>
                    <td className="border p-2">{user.name || "N/A"}</td>
                    <td className="border p-2">
                      <RoleSelector
                        currentRole={user.role}
                        onRoleChange={(role) =>
                          handleSetUserRole(user.id, role)
                        }
                      />
                    </td>
                    <td className="border p-2">
                      {user.banned ? (
                        <span className="text-red-600">
                          Banned {user.banReason && `(${user.banReason})`}
                        </span>
                      ) : (
                        <span className="text-green-600">Active</span>
                      )}
                    </td>
                    <td className="border p-2">
                      <div className="flex gap-2">
                        {user.banned ? (
                          <button
                            onClick={() => handleUnbanUser(user.id)}
                            className="px-2 py-1 bg-green-500 text-white rounded text-sm"
                          >
                            Unban
                          </button>
                        ) : (
                          <BanUserModal
                            userId={user.id}
                            onUserBanned={() =>
                              fetchUsers(currentPage, searchQuery)
                            }
                          />
                        )}
                        <ImpersonateUserButton userId={user.id} />
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          className="px-2 py-1 bg-red-500 text-white rounded text-sm"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />
        </>
      )}
    </div>
  );
};
```

#### 3. Create User Modal Component

```typescript
// components/admin/CreateUserModal.tsx
interface CreateUserFormData {
  email: string;
  password: string;
  name?: string;
  role: string | string[];
}

export const CreateUserModal: React.FC<{ onUserCreated: () => void }> = ({
  onUserCreated,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<CreateUserFormData>({
    email: "",
    password: "",
    name: "",
    role: "user",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await authClient.admin.createUser({
        email: formData.email,
        password: formData.password,
        name: formData.name,
        role: formData.role,
      });

      setIsOpen(false);
      setFormData({ email: "", password: "", name: "", role: "user" });
      onUserCreated();
    } catch (err: any) {
      setError(err.message || "Failed to create user");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-blue-500 text-white rounded"
      >
        Create User
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg w-96">
            <h3 className="text-lg font-semibold mb-4">Create New User</h3>

            {error && (
              <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">Email</label>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) =>
                    setFormData({ ...formData, email: e.target.value })
                  }
                  className="w-full p-2 border rounded"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">
                  Password
                </label>
                <input
                  type="password"
                  required
                  value={formData.password}
                  onChange={(e) =>
                    setFormData({ ...formData, password: e.target.value })
                  }
                  className="w-full p-2 border rounded"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">
                  Name (Optional)
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  className="w-full p-2 border rounded"
                />
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">Role</label>
                <select
                  value={
                    Array.isArray(formData.role)
                      ? formData.role[0]
                      : formData.role
                  }
                  onChange={(e) =>
                    setFormData({ ...formData, role: e.target.value })
                  }
                  className="w-full p-2 border rounded"
                >
                  <option value="user">User</option>
                  <option value="admin">Admin</option>
                </select>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className="px-4 py-2 text-gray-600 border rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
                >
                  {loading ? "Creating..." : "Create User"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};
```

#### 4. Session Management Component

```typescript
// components/admin/AdminSessionManagement.tsx
interface UserSession {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  impersonatedBy?: string;
}

export const AdminSessionManagement: React.FC = () => {
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchUserSessions = async (userId: string) => {
    if (!userId) return;

    setLoading(true);
    try {
      const { data } = await authClient.admin.listUserSessions({ userId });
      setSessions(data);
    } catch (error) {
      console.error("Failed to fetch user sessions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRevokeSession = async (sessionToken: string) => {
    try {
      await authClient.admin.revokeUserSession({ sessionToken });
      fetchUserSessions(selectedUserId);
    } catch (error) {
      console.error("Failed to revoke session:", error);
    }
  };

  const handleRevokeAllSessions = async (userId: string) => {
    if (
      !confirm("Are you sure you want to revoke all sessions for this user?")
    ) {
      return;
    }

    try {
      await authClient.admin.revokeUserSessions({ userId });
      fetchUserSessions(userId);
    } catch (error) {
      console.error("Failed to revoke all sessions:", error);
    }
  };

  return (
    <div className="session-management">
      <h2 className="text-lg font-semibold mb-4">Session Management</h2>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-1">Select User</label>
        <UserSelector
          onUserSelect={(userId) => {
            setSelectedUserId(userId);
            fetchUserSessions(userId);
          }}
        />
      </div>

      {selectedUserId && (
        <div className="mb-4">
          <button
            onClick={() => handleRevokeAllSessions(selectedUserId)}
            className="px-4 py-2 bg-red-500 text-white rounded"
          >
            Revoke All Sessions
          </button>
        </div>
      )}

      {loading ? (
        <div>Loading sessions...</div>
      ) : sessions.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border">
            <thead>
              <tr className="bg-gray-50">
                <th className="border p-2 text-left">Session ID</th>
                <th className="border p-2 text-left">IP Address</th>
                <th className="border p-2 text-left">User Agent</th>
                <th className="border p-2 text-left">Expires At</th>
                <th className="border p-2 text-left">Impersonated</th>
                <th className="border p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {sessions.map((session) => (
                <tr key={session.id}>
                  <td className="border p-2 font-mono text-sm">
                    {session.id.substring(0, 8)}...
                  </td>
                  <td className="border p-2">{session.ipAddress || "N/A"}</td>
                  <td className="border p-2 max-w-xs truncate">
                    {session.userAgent || "N/A"}
                  </td>
                  <td className="border p-2">
                    {new Date(session.expiresAt).toLocaleString()}
                  </td>
                  <td className="border p-2">
                    {session.impersonatedBy ? (
                      <span className="text-orange-600">Yes</span>
                    ) : (
                      <span className="text-gray-600">No</span>
                    )}
                  </td>
                  <td className="border p-2">
                    <button
                      onClick={() => handleRevokeSession(session.token)}
                      className="px-2 py-1 bg-red-500 text-white rounded text-sm"
                    >
                      Revoke
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : selectedUserId ? (
        <div>No active sessions found for this user.</div>
      ) : (
        <div>Select a user to view their sessions.</div>
      )}
    </div>
  );
};
```

#### 5. User Impersonation Component

```typescript
// components/admin/ImpersonateUserButton.tsx
export const ImpersonateUserButton: React.FC<{ userId: string }> = ({
  userId,
}) => {
  const [loading, setLoading] = useState(false);

  const handleImpersonate = async () => {
    if (!confirm("Are you sure you want to impersonate this user?")) {
      return;
    }

    setLoading(true);
    try {
      await authClient.admin.impersonateUser({ userId });
      // Redirect to dashboard as the impersonated user
      window.location.href = "/dashboard";
    } catch (error) {
      console.error("Failed to impersonate user:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleImpersonate}
      disabled={loading}
      className="px-2 py-1 bg-orange-500 text-white rounded text-sm disabled:opacity-50"
    >
      {loading ? "Impersonating..." : "Impersonate"}
    </button>
  );
};

// Component to stop impersonation
export const StopImpersonationButton: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleStopImpersonation = async () => {
    setLoading(true);
    try {
      await authClient.admin.stopImpersonating();
      // Redirect back to admin dashboard
      window.location.href = "/admin";
    } catch (error) {
      console.error("Failed to stop impersonation:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleStopImpersonation}
      disabled={loading}
      className="px-4 py-2 bg-gray-500 text-white rounded disabled:opacity-50"
    >
      {loading ? "Stopping..." : "Stop Impersonation"}
    </button>
  );
};
```

#### 6. Permission Check Hook

```typescript
// hooks/useAdminPermissions.ts
export const useAdminPermissions = () => {
  const { data: user } = useUser();
  const [permissions, setPermissions] = useState<Record<string, boolean>>({});

  const isAdmin = useMemo(() => {
    if (!user) return false;
    const userRole = Array.isArray(user.role) ? user.role : [user.role];
    return userRole.includes("admin");
  }, [user]);

  const checkPermission = useCallback(
    async (resource: string, actions: string[]) => {
      if (!isAdmin) return false;

      try {
        const { data } = await authClient.admin.hasPermission({
          permissions: { [resource]: actions },
        });
        return data;
      } catch (error) {
        console.error("Permission check failed:", error);
        return false;
      }
    },
    [isAdmin]
  );

  const checkRolePermission = useCallback(
    (role: string, resource: string, actions: string[]) => {
      return authClient.admin.checkRolePermission({
        role,
        permissions: { [resource]: actions },
      });
    },
    []
  );

  return {
    isAdmin,
    user,
    checkPermission,
    checkRolePermission,
    permissions,
  };
};
```

## Organization Plugin (Multi-Tenant) Implementation

### Overview

The organization plugin enables multi-tenant functionality, allowing users to create and manage organizations with role-based access control, member management, and team structures. This provides workspace isolation and collaborative features essential for the broker portfolio platform.

### Organization Client Configuration

```typescript
// lib/auth-client.ts (updated)
import { createAuthClient } from "better-auth/react";
import { organizationClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    organizationClient({
      // Enable teams functionality
      teams: {
        enabled: true,
      },
    }),
  ],
  fetchOptions: {
    onError: (ctx) => {
      console.error("Auth error:", ctx.error);
    },
    onSuccess: (ctx) => {
      console.log("Auth success:", ctx.response);
    },
  },
});

// Export organization-specific hooks
export const {
  useSession,
  useUser,
  signIn,
  signUp,
  signOut,
  useListOrganizations,
  useActiveOrganization,
} = authClient;
```

### Organization Management Components

#### 1. Organization Context Provider

```typescript
// contexts/OrganizationContext.tsx
import { useActiveOrganization, useListOrganizations } from "@/lib/auth-client";

interface OrganizationContextType {
  activeOrganization: Organization | null;
  organizations: Organization[];
  loading: boolean;
  error: string | null;
  setActiveOrganization: (organizationId: string | null) => Promise<void>;
  createOrganization: (data: CreateOrganizationData) => Promise<Organization>;
  updateOrganization: (
    organizationId: string,
    data: Partial<Organization>
  ) => Promise<void>;
  deleteOrganization: (organizationId: string) => Promise<void>;
}

export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    data: activeOrganization,
    isPending: activeLoading,
    error: activeError,
  } = useActiveOrganization();

  const {
    data: organizations,
    isPending: listLoading,
    error: listError,
    refetch: refetchOrganizations,
  } = useListOrganizations();

  const [globalError, setGlobalError] = useState<string | null>(null);

  const loading = activeLoading || listLoading;
  const error = activeError?.message || listError?.message || globalError;

  const setActiveOrganization = useCallback(
    async (organizationId: string | null) => {
      try {
        await authClient.organization.setActive({ organizationId });
        // Refetch to update UI
        window.location.reload(); // Simple approach, could use more sophisticated state management
      } catch (err: any) {
        setGlobalError(err.message);
      }
    },
    []
  );

  const createOrganization = useCallback(
    async (data: CreateOrganizationData) => {
      try {
        const { data: organization } = await authClient.organization.create(
          data
        );
        refetchOrganizations();
        return organization;
      } catch (err: any) {
        setGlobalError(err.message);
        throw err;
      }
    },
    [refetchOrganizations]
  );

  const updateOrganization = useCallback(
    async (organizationId: string, data: Partial<Organization>) => {
      try {
        await authClient.organization.update({ organizationId, data });
        refetchOrganizations();
      } catch (err: any) {
        setGlobalError(err.message);
        throw err;
      }
    },
    [refetchOrganizations]
  );

  const deleteOrganization = useCallback(
    async (organizationId: string) => {
      try {
        await authClient.organization.delete({ organizationId });
        refetchOrganizations();
      } catch (err: any) {
        setGlobalError(err.message);
        throw err;
      }
    },
    [refetchOrganizations]
  );

  const value: OrganizationContextType = {
    activeOrganization,
    organizations: organizations || [],
    loading,
    error,
    setActiveOrganization,
    createOrganization,
    updateOrganization,
    deleteOrganization,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};
```

#### 2. Organization Selector Component

```typescript
// components/organization/OrganizationSelector.tsx
import { useOrganization } from "@/contexts/OrganizationContext";

export const OrganizationSelector: React.FC = () => {
  const { activeOrganization, organizations, loading, setActiveOrganization } =
    useOrganization();

  const [isOpen, setIsOpen] = useState(false);

  const handleOrganizationChange = async (organizationId: string | null) => {
    await setActiveOrganization(organizationId);
    setIsOpen(false);
  };

  if (loading) {
    return <div className="animate-pulse bg-gray-200 h-8 w-48 rounded"></div>;
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 border rounded-md bg-white hover:bg-gray-50"
      >
        <div className="flex items-center gap-2">
          {activeOrganization?.logo && (
            <img
              src={activeOrganization.logo}
              alt={activeOrganization.name}
              className="w-6 h-6 rounded"
            />
          )}
          <span className="font-medium">
            {activeOrganization?.name || "Select Organization"}
          </span>
        </div>
        <ChevronDownIcon className="w-4 h-4" />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-white border rounded-md shadow-lg z-50">
          <div className="py-1">
            {organizations.map((org) => (
              <button
                key={org.id}
                onClick={() => handleOrganizationChange(org.id)}
                className={`w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center gap-2 ${
                  activeOrganization?.id === org.id
                    ? "bg-blue-50 text-blue-600"
                    : ""
                }`}
              >
                {org.logo && (
                  <img
                    src={org.logo}
                    alt={org.name}
                    className="w-6 h-6 rounded"
                  />
                )}
                <div>
                  <div className="font-medium">{org.name}</div>
                  <div className="text-sm text-gray-500">@{org.slug}</div>
                </div>
              </button>
            ))}
            <hr className="my-1" />
            <button
              onClick={() => handleOrganizationChange(null)}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 text-gray-600"
            >
              No Organization
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
```

#### 3. Create Organization Modal

```typescript
// components/organization/CreateOrganizationModal.tsx
interface CreateOrganizationFormData {
  name: string;
  slug: string;
  logo?: string;
  metadata?: Record<string, any>;
}

export const CreateOrganizationModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (organization: Organization) => void;
}> = ({ isOpen, onClose, onSuccess }) => {
  const { createOrganization } = useOrganization();
  const [formData, setFormData] = useState<CreateOrganizationFormData>({
    name: "",
    slug: "",
    logo: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name) {
      const generatedSlug = formData.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
      setFormData((prev) => ({ ...prev, slug: generatedSlug }));
    }
  }, [formData.name]);

  // Check slug availability
  const checkSlugAvailability = useCallback(
    debounce(async (slug: string) => {
      if (!slug) {
        setSlugAvailable(null);
        return;
      }

      try {
        const { data } = await authClient.organization.checkSlug({ slug });
        setSlugAvailable(data.available);
      } catch (err) {
        setSlugAvailable(false);
      }
    }, 500),
    []
  );

  useEffect(() => {
    checkSlugAvailability(formData.slug);
  }, [formData.slug, checkSlugAvailability]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const organization = await createOrganization({
        name: formData.name,
        slug: formData.slug,
        logo: formData.logo || undefined,
        metadata: formData.metadata,
      });

      onSuccess?.(organization);
      onClose();
      setFormData({ name: "", slug: "", logo: "" });
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Create Organization</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XIcon className="w-6 h-6" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              Organization Name
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              className="w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Acme Real Estate"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              Organization Slug
            </label>
            <div className="relative">
              <input
                type="text"
                required
                value={formData.slug}
                onChange={(e) =>
                  setFormData({ ...formData, slug: e.target.value })
                }
                className={`w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  slugAvailable === false ? "border-red-500" : ""
                } ${slugAvailable === true ? "border-green-500" : ""}`}
                placeholder="acme-real-estate"
              />
              {slugAvailable === false && (
                <div className="absolute right-3 top-3">
                  <XCircleIcon className="w-5 h-5 text-red-500" />
                </div>
              )}
              {slugAvailable === true && (
                <div className="absolute right-3 top-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-500" />
                </div>
              )}
            </div>
            {slugAvailable === false && (
              <p className="text-sm text-red-600 mt-1">
                This slug is already taken
              </p>
            )}
            <p className="text-sm text-gray-500 mt-1">
              URL: yourapp.com/{formData.slug}
            </p>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium mb-2">
              Logo URL (Optional)
            </label>
            <input
              type="url"
              value={formData.logo}
              onChange={(e) =>
                setFormData({ ...formData, logo: e.target.value })
              }
              className="w-full p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://example.com/logo.png"
            />
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || slugAvailable === false}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? "Creating..." : "Create Organization"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
```

#### 4. Organization Member Management

```typescript
// components/organization/MemberManagement.tsx
export const MemberManagement: React.FC = () => {
  const { activeOrganization } = useOrganization();
  const [members, setMembers] = useState<OrganizationMember[]>([]);
  const [invitations, setInvitations] = useState<OrganizationInvitation[]>([]);
  const [loading, setLoading] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);

  const fetchMembers = useCallback(async () => {
    if (!activeOrganization) return;

    setLoading(true);
    try {
      const { data: membersData } = await authClient.organization.listMembers({
        organizationId: activeOrganization.id,
      });
      setMembers(membersData);

      const { data: invitationsData } =
        await authClient.organization.listInvitations({
          organizationId: activeOrganization.id,
        });
      setInvitations(invitationsData);
    } catch (error) {
      console.error("Failed to fetch members:", error);
    } finally {
      setLoading(false);
    }
  }, [activeOrganization]);

  useEffect(() => {
    fetchMembers();
  }, [fetchMembers]);

  const handleRemoveMember = async (memberIdOrEmail: string) => {
    if (!confirm("Are you sure you want to remove this member?")) return;

    try {
      await authClient.organization.removeMember({
        memberIdOrEmail,
        organizationId: activeOrganization?.id,
      });
      fetchMembers();
    } catch (error) {
      console.error("Failed to remove member:", error);
    }
  };

  const handleUpdateMemberRole = async (
    memberId: string,
    role: string | string[]
  ) => {
    try {
      await authClient.organization.updateMemberRole({
        memberId,
        role,
        organizationId: activeOrganization?.id,
      });
      fetchMembers();
    } catch (error) {
      console.error("Failed to update member role:", error);
    }
  };

  const handleCancelInvitation = async (invitationId: string) => {
    try {
      await authClient.organization.cancelInvitation({ invitationId });
      fetchMembers();
    } catch (error) {
      console.error("Failed to cancel invitation:", error);
    }
  };

  if (!activeOrganization) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          Please select an organization to manage members.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Members</h2>
        <button
          onClick={() => setShowInviteModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Invite Member
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading members...</div>
      ) : (
        <div className="space-y-6">
          {/* Active Members */}
          <div>
            <h3 className="text-lg font-semibold mb-4">
              Active Members ({members.length})
            </h3>
            <div className="bg-white border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Member
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joined
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {members.map((member) => (
                    <tr key={member.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {member.user.name?.[0] ||
                                  member.user.email[0].toUpperCase()}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {member.user.name || "No name"}
                            </div>
                            <div className="text-sm text-gray-500">
                              {member.user.email}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <RoleSelector
                          currentRole={member.role}
                          onRoleChange={(role) =>
                            handleUpdateMemberRole(member.id, role)
                          }
                          disabled={member.role === "owner"}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(member.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        {member.role !== "owner" && (
                          <button
                            onClick={() =>
                              handleRemoveMember(member.user.email)
                            }
                            className="text-red-600 hover:text-red-900"
                          >
                            Remove
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pending Invitations */}
          {invitations.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">
                Pending Invitations ({invitations.length})
              </h3>
              <div className="bg-white border rounded-lg overflow-hidden">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Invited
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Expires
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {invitations.map((invitation) => (
                      <tr key={invitation.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {invitation.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            {Array.isArray(invitation.role)
                              ? invitation.role.join(", ")
                              : invitation.role}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(invitation.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(invitation.expiresAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() =>
                              handleCancelInvitation(invitation.id)
                            }
                            className="text-red-600 hover:text-red-900"
                          >
                            Cancel
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Invite Member Modal */}
      <InviteMemberModal
        isOpen={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        onSuccess={fetchMembers}
        organizationId={activeOrganization.id}
      />
    </div>
  );
};
```

#### 5. Team Management Components

```typescript
// components/organization/TeamManagement.tsx
export const TeamManagement: React.FC = () => {
  const { activeOrganization } = useOrganization();
  const [teams, setTeams] = useState<Team[]>([]);
  const [activeTeam, setActiveTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const fetchTeams = useCallback(async () => {
    if (!activeOrganization) return;

    setLoading(true);
    try {
      const { data } = await authClient.organization.listTeams({
        organizationId: activeOrganization.id,
      });
      setTeams(data);
    } catch (error) {
      console.error("Failed to fetch teams:", error);
    } finally {
      setLoading(false);
    }
  }, [activeOrganization]);

  useEffect(() => {
    fetchTeams();
  }, [fetchTeams]);

  const handleCreateTeam = async (name: string) => {
    try {
      await authClient.organization.createTeam({
        name,
        organizationId: activeOrganization?.id,
      });
      fetchTeams();
    } catch (error) {
      console.error("Failed to create team:", error);
    }
  };

  const handleDeleteTeam = async (teamId: string) => {
    if (!confirm("Are you sure you want to delete this team?")) return;

    try {
      await authClient.organization.removeTeam({
        teamId,
        organizationId: activeOrganization?.id,
      });
      fetchTeams();
    } catch (error) {
      console.error("Failed to delete team:", error);
    }
  };

  const handleSetActiveTeam = async (teamId: string | null) => {
    try {
      await authClient.organization.setActiveTeam({ teamId });
      setActiveTeam(teams.find((t) => t.id === teamId) || null);
    } catch (error) {
      console.error("Failed to set active team:", error);
    }
  };

  if (!activeOrganization) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">
          Please select an organization to manage teams.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Teams</h2>
        <button
          onClick={() => setShowCreateModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Create Team
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading teams...</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {teams.map((team) => (
            <div
              key={team.id}
              className={`border rounded-lg p-6 ${
                activeTeam?.id === team.id
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200"
              }`}
            >
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-lg font-semibold">{team.name}</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleSetActiveTeam(team.id)}
                    className={`px-2 py-1 text-xs rounded ${
                      activeTeam?.id === team.id
                        ? "bg-blue-600 text-white"
                        : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                    }`}
                  >
                    {activeTeam?.id === team.id ? "Active" : "Set Active"}
                  </button>
                  <button
                    onClick={() => handleDeleteTeam(team.id)}
                    className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    Delete
                  </button>
                </div>
              </div>

              <div className="text-sm text-gray-500">
                Created: {new Date(team.createdAt).toLocaleDateString()}
              </div>

              <TeamMembersList teamId={team.id} />
            </div>
          ))}
        </div>
      )}

      <CreateTeamModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handleCreateTeam}
      />
    </div>
  );
};
```

### Organization Access Control

#### 1. Permission-Based Components

```typescript
// components/organization/PermissionGuard.tsx
interface PermissionGuardProps {
  children: React.ReactNode;
  permissions: Record<string, string[]>;
  fallback?: React.ReactNode;
  requireAll?: boolean; // true = require all permissions, false = require any
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permissions,
  fallback = null,
  requireAll = true,
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        const { data } = await authClient.organization.hasPermission({
          permissions,
        });
        setHasPermission(data);
      } catch (error) {
        console.error("Permission check failed:", error);
        setHasPermission(false);
      }
    };

    checkPermissions();
  }, [permissions]);

  if (hasPermission === null) {
    return <div className="animate-pulse bg-gray-200 h-4 w-full rounded"></div>;
  }

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Usage example
export const OrganizationSettings: React.FC = () => {
  return (
    <div>
      <h1>Organization Settings</h1>

      <PermissionGuard
        permissions={{ organization: ["update"] }}
        fallback={
          <div>You don't have permission to modify organization settings.</div>
        }
      >
        <OrganizationSettingsForm />
      </PermissionGuard>

      <PermissionGuard
        permissions={{ member: ["create"] }}
        fallback={<div>You don't have permission to invite members.</div>}
      >
        <InviteMemberButton />
      </PermissionGuard>
    </div>
  );
};
```

#### 2. Role-Based Hook

```typescript
// hooks/useOrganizationPermissions.ts
export const useOrganizationPermissions = () => {
  const { activeOrganization } = useOrganization();
  const [member, setMember] = useState<OrganizationMember | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchActiveMember = async () => {
      if (!activeOrganization) return;

      setLoading(true);
      try {
        const { data } = await authClient.organization.getActiveMember();
        setMember(data);
      } catch (error) {
        console.error("Failed to fetch active member:", error);
        setMember(null);
      } finally {
        setLoading(false);
      }
    };

    fetchActiveMember();
  }, [activeOrganization]);

  const hasRole = useCallback(
    (role: string | string[]) => {
      if (!member) return false;

      const memberRoles = Array.isArray(member.role)
        ? member.role
        : [member.role];
      const requiredRoles = Array.isArray(role) ? role : [role];

      return requiredRoles.some((r) => memberRoles.includes(r));
    },
    [member]
  );

  const hasPermission = useCallback(
    async (permissions: Record<string, string[]>) => {
      try {
        const { data } = await authClient.organization.hasPermission({
          permissions,
        });
        return data;
      } catch (error) {
        console.error("Permission check failed:", error);
        return false;
      }
    },
    []
  );

  const checkRolePermission = useCallback(
    (role: string, permissions: Record<string, string[]>) => {
      return authClient.organization.checkRolePermission({ role, permissions });
    },
    []
  );

  return {
    member,
    loading,
    hasRole,
    hasPermission,
    checkRolePermission,
    isOwner: hasRole("owner"),
    isAdmin: hasRole(["owner", "admin"]),
    isMember: hasRole(["owner", "admin", "member"]),
  };
};
```

## Magic Link Authentication

### Overview

Magic link authentication provides a passwordless sign-in experience where users receive a secure link via email to authenticate. This feature enhances user experience by eliminating the need to remember passwords while maintaining security through time-limited, single-use tokens.

### Magic Link Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as Auth Server
    participant E as Email Service

    U->>F: Enter email address
    F->>A: POST /sign-in/magic-link
    A->>E: Send magic link email
    A->>F: Success response
    F->>U: "Check your email" message
    
    Note over U,E: User checks email
    U->>E: Click magic link
    E->>A: GET /magic-link/verify?token=...
    A->>A: Verify token & create session
    A->>F: Redirect to callbackURL
    F->>U: User is authenticated
```

### Configuration Options

The magic link plugin supports several configuration options that should be set on the server side:

```typescript
// Server-side configuration (for reference)
import { magicLink } from "better-auth/plugins";

export const auth = betterAuth({
  plugins: [
    magicLink({
      // Required: Function to send magic link emails
      sendMagicLink: async ({ email, token, url }, request) => {
        // Implementation depends on email service (SendGrid, Resend, etc.)
        await emailService.send({
          to: email,
          subject: "Sign in to your account",
          html: `
            <h1>Sign in to your account</h1>
            <p>Click the link below to sign in:</p>
            <a href="${url}">Sign In</a>
            <p>This link will expire in 5 minutes.</p>
          `,
        });
      },
      
      // Optional: Token expiration time (default: 300 seconds / 5 minutes)
      expiresIn: 300,
      
      // Optional: Disable automatic user registration (default: false)
      disableSignUp: false,
      
      // Optional: Custom token generation (default: secure random string)
      generateToken: (email) => {
        // Return a cryptographically secure token
        return generateSecureToken();
      },
      
      // Optional: Token storage method (default: "plain")
      storeToken: "hashed", // or "plain" or custom hasher
    }),
  ],
});
```

### Client-Side Implementation

#### Magic Link Sign-In Integration

```typescript
// Enhanced sign-in form with magic link option
export const EnhancedSignInForm: React.FC = () => {
  const [authMethod, setAuthMethod] = useState<"password" | "magiclink">("password");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [magicLinkSent, setMagicLinkSent] = useState(false);

  const handlePasswordSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const { data, error: authError } = await authClient.signIn.email({
      email,
      password,
    });

    if (authError) {
      setError(authError.message);
    } else {
      window.location.href = "/dashboard";
    }

    setIsLoading(false);
  };

  const handleMagicLinkSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    const { data, error: authError } = await authClient.signIn.magicLink({
      email,
      callbackURL: "/dashboard",
      newUserCallbackURL: "/welcome",
      errorCallbackURL: "/sign-in?error=magic-link-failed",
    });

    if (authError) {
      setError(authError.message);
    } else {
      setMagicLinkSent(true);
    }

    setIsLoading(false);
  };

  if (magicLinkSent) {
    return (
      <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Check your email</h2>
          <p className="text-gray-600 mb-4">
            We've sent a magic link to <strong>{email}</strong>
          </p>
          <p className="text-sm text-gray-500 mb-6">
            Click the link in the email to sign in. The link will expire in 5 minutes.
          </p>
          <button
            onClick={() => {
              setMagicLinkSent(false);
              setEmail("");
            }}
            className="text-blue-600 hover:underline"
          >
            Use a different email
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow">
      <h2 className="text-2xl font-bold text-center mb-6">Sign In</h2>
      
      {/* Auth method toggle */}
      <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
        <button
          type="button"
          onClick={() => setAuthMethod("password")}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            authMethod === "password"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          Password
        </button>
        <button
          type="button"
          onClick={() => setAuthMethod("magiclink")}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            authMethod === "magiclink"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          Magic Link
        </button>
      </div>

      <form onSubmit={authMethod === "password" ? handlePasswordSignIn : handleMagicLinkSignIn}>
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email address
          </label>
          <input
            id="email"
            type="email"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your email"
          />
        </div>

        {authMethod === "password" && (
          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter your password"
            />
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <button
          type="submit"
          disabled={isLoading}
          className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isLoading 
            ? (authMethod === "password" ? "Signing in..." : "Sending...")
            : (authMethod === "password" ? "Sign In" : "Send Magic Link")
          }
        </button>
      </form>

      {authMethod === "password" && (
        <div className="mt-4 text-center">
          <a href="/forgot-password" className="text-sm text-blue-600 hover:underline">
            Forgot your password?
          </a>
        </div>
      )}
    </div>
  );
};
```

### Route Configuration

```typescript
// routes/auth.tsx
import { createRoute } from "@tanstack/react-router";
import { MagicLinkVerification } from "@/components/auth/MagicLinkVerification";

export const magicLinkVerifyRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: "/auth/magic-link/verify",
  component: MagicLinkVerification,
});
```

### Error Handling

Magic link authentication includes specific error scenarios that should be handled:

```typescript
// utils/magic-link-errors.ts
export const MAGIC_LINK_ERRORS = {
  INVALID_TOKEN: "The magic link is invalid or has expired",
  EXPIRED_TOKEN: "The magic link has expired. Please request a new one",
  ALREADY_USED: "This magic link has already been used",
  EMAIL_NOT_FOUND: "No account found with this email address",
  SEND_FAILED: "Failed to send magic link. Please try again",
  RATE_LIMITED: "Too many requests. Please wait before requesting another magic link",
} as const;

export const handleMagicLinkError = (error: string) => {
  // Map server errors to user-friendly messages
  switch (error) {
    case "INVALID_TOKEN":
      return MAGIC_LINK_ERRORS.INVALID_TOKEN;
    case "EXPIRED_TOKEN":
      return MAGIC_LINK_ERRORS.EXPIRED_TOKEN;
    case "ALREADY_USED":
      return MAGIC_LINK_ERRORS.ALREADY_USED;
    default:
      return "An error occurred during authentication. Please try again.";
  }
};
```

### Security Considerations

1. **Token Expiration**: Magic links expire after 5 minutes by default
2. **Single Use**: Each token can only be used once
3. **Secure Generation**: Tokens are cryptographically secure random strings
4. **Rate Limiting**: Server should implement rate limiting for magic link requests
5. **Email Verification**: Magic links inherently verify email ownership
6. **HTTPS Required**: Magic links should only be sent over HTTPS in production

### User Experience Enhancements

```typescript
// components/auth/MagicLinkStatus.tsx
export const MagicLinkStatus: React.FC<{ email: string; onResend: () => void }> = ({
  email,
  onResend,
}) => {
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="text-center p-6">
      <div className="mb-4">
        <svg className="w-16 h-16 mx-auto text-blue-600 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      </div>
      
      <h2 className="text-2xl font-bold mb-2">Check your email</h2>
      <p className="text-gray-600 mb-4">
        We've sent a magic link to <strong>{email}</strong>
      </p>
      
      {timeLeft > 0 ? (
        <p className="text-sm text-gray-500 mb-4">
          Link expires in {formatTime(timeLeft)}
        </p>
      ) : (
        <p className="text-sm text-red-500 mb-4">
          The magic link has expired
        </p>
      )}

      <div className="space-y-2">
        {canResend && (
          <button
            onClick={onResend}
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Send New Magic Link
          </button>
        )}
        
        <button
          onClick={() => window.location.href = "/sign-in"}
          className="w-full py-2 px-4 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Back to Sign In
        </button>
      </div>
    </div>
  );
};
```

## Data Models

### Core Authentication Models

These models are handled by the better-auth server and accessed through the client hooks.

#### User Model
```typescript
interface User {
  id: string;
  email: string;
  emailVerified: boolean;
  name?: string;
  image?: string;
  createdAt: Date;
  updatedAt: Date;
  // Stripe integration fields (when enabled)
  stripeCustomerId?: string;
}
```

#### Session Model
```typescript
interface Session {
  id: string;
  userId: string;
  token: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  impersonatedBy?: string;
}
```

### Stripe Integration Models (Optional)

These models are added when Stripe integration is enabled on the better-auth server.

#### Subscription Model
```typescript
interface Subscription {
  id: string;
  plan: string;
  referenceId: string; // Usually userId, but can be organizationId for team plans
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  status: 'active' | 'canceled' | 'trialing' | 'incomplete' | 'past_due';
  periodStart?: Date;
  periodEnd?: Date;
  cancelAtPeriodEnd?: boolean;
  seats?: number; // For team plans
  trialStart?: Date;
  trialEnd?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Plan Configuration Model
```typescript
interface PlanConfig {
  name: string;
  priceId: string; // Stripe price ID
  annualDiscountPriceId?: string; // Optional annual pricing
  limits: Record<string, number>; // Feature limits (-1 for unlimited)
  group?: string; // Plan grouping
  freeTrial?: {
    days: number;
  };
}
```

#### Subscription Limits Model
```typescript
interface SubscriptionLimits {
  // Example limits for a broker platform
  listings: number; // Number of property listings
  teamMembers: number; // Number of team members
  storage: number; // Storage in GB
  apiCalls: number; // API calls per month
  reports: number; // Number of reports per month
  // -1 indicates unlimited
}
```

### Client-Side Data Types

All authentication and organization-related types should be centralized in `src/models/auth.ts` for better maintainability and type safety across the application.

```typescript
// src/models/auth.ts
// Centralized authentication and organization types from better-auth client
export interface User {
  id: string;
  email: string;
  emailVerified: boolean;
  name?: string;
  image?: string;
  role?: string | string[]; // Admin plugin adds role field
  banned?: boolean; // Admin plugin adds banned field
  banReason?: string; // Admin plugin adds ban reason
  banExpires?: Date; // Admin plugin adds ban expiration
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: Date;
  token: string;
  ipAddress?: string;
  userAgent?: string;
  impersonatedBy?: string; // Admin plugin adds impersonation tracking
  activeOrganizationId?: string; // Organization plugin adds active org
  activeTeamId?: string; // Organization plugin adds active team
}

export interface Account {
  id: string;
  userId: string;
  accountId: string;
  providerId: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

// Admin plugin types
export interface AdminUser extends User {
  role: string | string[];
  banned: boolean;
  banReason?: string;
  banExpires?: Date;
}

export interface AdminCreateUserData {
  email: string;
  password: string;
  name?: string;
  role?: string | string[];
  data?: Record<string, any>;
}

export interface AdminListUsersQuery {
  searchValue?: string;
  searchField?: "email" | "name";
  searchOperator?: "contains" | "starts_with" | "ends_with";
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortDirection?: "asc" | "desc";
  filterField?: string;
  filterValue?: string | number | boolean;
  filterOperator?: "eq" | "ne" | "lt" | "lte" | "gt" | "gte";
}

export interface AdminListUsersResponse {
  users: AdminUser[];
  total: number;
  limit?: number;
  offset?: number;
}

export interface AdminPermissionCheck {
  permissions: Record<string, string[]>;
  userId?: string;
  role?: string;
}

// Organization plugin types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationMember {
  id: string;
  organizationId: string;
  userId: string;
  user: User;
  role: "owner" | "admin" | "member" | string | string[];
  createdAt: Date;
}

export interface OrganizationInvitation {
  id: string;
  email: string;
  organizationId: string;
  organization: Organization;
  inviterId: string;
  inviter: OrganizationMember;
  role: string | string[];
  status: "pending" | "accepted" | "rejected" | "expired";
  teamId?: string;
  expiresAt: Date;
  createdAt: Date;
}

export interface Team {
  id: string;
  name: string;
  organizationId: string;
  organization: Organization;
  createdAt: Date;
  updatedAt: Date;
}

export interface TeamMember {
  id: string;
  teamId: string;
  team: Team;
  userId: string;
  user: User;
  createdAt: Date;
}

// Organization-specific form data types
export interface CreateOrganizationData {
  name: string;
  slug: string;
  logo?: string;
  metadata?: Record<string, any>;
  keepCurrentActiveOrganization?: boolean;
}

export interface UpdateOrganizationData {
  name?: string;
  slug?: string;
  logo?: string;
  metadata?: Record<string, any> | null;
}

export interface InviteMemberData {
  email: string;
  role: string | string[];
  organizationId?: string;
  teamId?: string;
  resend?: boolean;
}

export interface CreateTeamData {
  name: string;
  organizationId?: string;
}

export interface UpdateTeamData {
  name?: string;
  organizationId?: string;
}
```

### Type Organization Structure

The centralized models file should be organized as follows:

```typescript
// src/models/auth.ts

// Core authentication types
export interface User { ... }
export interface Session { ... }
export interface Account { ... }

// Admin plugin types
export interface AdminUser extends User { ... }
export interface AdminCreateUserData { ... }
export interface AdminListUsersQuery { ... }
export interface AdminListUsersResponse { ... }
export interface AdminPermissionCheck { ... }

// Organization plugin types
export interface Organization { ... }
export interface OrganizationMember { ... }
export interface OrganizationInvitation { ... }
export interface Team { ... }
export interface TeamMember { ... }

// Form data types
export interface CreateOrganizationData { ... }
export interface UpdateOrganizationData { ... }
export interface InviteMemberData { ... }
export interface CreateTeamData { ... }
export interface UpdateTeamData { ... }

// Utility types
export type OrganizationRole = "owner" | "admin" | "member";
export type InvitationStatus = "pending" | "accepted" | "rejected" | "expired";
```

### Import Usage

Components and hooks should import types from the centralized models file:

```typescript
// Example component import
import type { 
  User, 
  Organization, 
  OrganizationMember, 
  CreateOrganizationData 
} from "@/models/auth";

// Example hook import
import type { 
  AdminUser, 
  AdminListUsersQuery, 
  AdminPermissionCheck 
} from "@/models/auth";
```

## Error Handling

### Error Types and Handling Strategy

#### 1. Authentication Errors

```typescript
// types/auth-errors.ts
import { AUTH_ERROR_CODES } from "@/lib/auth-client";

// Use better-auth's built-in error codes
export type AuthErrorCode = keyof typeof AUTH_ERROR_CODES;

export interface AuthError {
  code?: AuthErrorCode;
  message: string;
  status?: number;
  statusText?: string;
}

// Custom error messages for better UX
export const ERROR_MESSAGES: Partial<Record<AuthErrorCode, string>> = {
  USER_ALREADY_EXISTS: "An account with this email already exists",
  INVALID_CREDENTIALS: "Invalid email or password",
  WEAK_PASSWORD: "Password must be at least 8 characters long",
  EMAIL_NOT_VERIFIED: "Please verify your email before signing in",
  RATE_LIMITED: "Too many attempts. Please try again later",
};
```

#### 2. Error Handling Hook

```typescript
// hooks/useAuthError.ts
import { AUTH_ERROR_CODES, ERROR_MESSAGES } from "@/types/auth-errors";

export const useAuthError = () => {
  const [error, setError] = useState<AuthError | null>(null);

  const handleError = (error: unknown) => {
    if (error && typeof error === "object" && "message" in error) {
      const authError = error as AuthError;
      const friendlyMessage = authError.code
        ? ERROR_MESSAGES[authError.code] || authError.message
        : authError.message;

      setError({
        ...authError,
        message: friendlyMessage,
      });
    } else {
      setError({
        message: "An unexpected error occurred",
        code: undefined,
      });
    }
  };

  const clearError = () => setError(null);

  return { error, handleError, clearError };
};
```

#### 3. Global Error Boundary

```typescript
// components/ErrorBoundary.tsx
export class AuthErrorBoundary extends React.Component {
  // Error boundary for authentication-related errors
}
```

## Testing Strategy

### Authentication Testing

#### Unit Tests
- Auth client configuration and initialization
- Form validation logic
- Error handling functions
- Session management utilities
- Route protection logic

#### Integration Tests
- Sign up flow with form submission
- Sign in flow with various providers
- Password reset flow
- Profile update functionality
- Session persistence across page refreshes

#### E2E Tests
- Complete user registration and login journey
- Social authentication flows
- Protected route access and redirects
- Multi-tab session synchronization

### Stripe Integration Testing (When Enabled)

#### Unit Tests
- Subscription state management
- Feature gate logic
- Usage limit calculations
- Plan comparison utilities
- Stripe client method calls

#### Integration Tests
- Subscription upgrade flow (mock Stripe)
- Subscription cancellation and restoration
- Feature access based on subscription status
- Usage limit enforcement
- Webhook event processing (server-side)

#### E2E Tests with Stripe Test Mode
- Complete subscription purchase flow
- Plan upgrade and downgrade scenarios
- Trial period expiration handling
- Payment failure scenarios
- Billing portal integration

#### Mock Strategies
```typescript
// Mock Stripe client for testing
export const mockStripeClient = {
  upgrade: jest.fn(),
  cancel: jest.fn(),
  restore: jest.fn(),
  list: jest.fn(),
  useSubscriptions: () => ({
    data: mockSubscriptions,
    isLoading: false,
    error: null,
  }),
};

// Mock subscription data
export const mockSubscriptions = [
  {
    id: 'sub_123',
    plan: 'pro',
    referenceId: 'user_123',
    status: 'active',
    limits: {
      listings: 100,
      teamMembers: 5,
      storage: 50,
    },
  },
];
```

### Unit Testing

- **Auth Client Functions**: Test all authentication methods (signIn, signUp, signOut)
- **Form Validation**: Test form validation logic and error handling
- **Protected Routes**: Test route protection behavior in different environments
- **Error Handling**: Test error scenarios and user feedback

### Integration Testing

- **Authentication Flow**: End-to-end sign up and sign in flows
- **Session Management**: Test session persistence and refresh
- **Social Authentication**: Test OAuth flows with mock providers
- **Password Reset**: Test complete password reset workflow

### E2E Testing

- **User Registration**: Complete user journey from sign up to dashboard
- **Authentication States**: Test authenticated and unauthenticated states
- **Route Protection**: Test access to protected and public routes
- **Profile Management**: Test profile viewing and editing

### Admin Testing

- **Admin Functions**: Test all admin operations (create user, ban/unban, role management)
- **Permission Checks**: Test role-based access control and permission validation
- **User Impersonation**: Test impersonation flow and session management
- **Session Management**: Test session listing, revocation, and cleanup

### Testing Tools

- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: Jest + MSW (Mock Service Worker)
- **E2E Tests**: Playwright or Cypress
- **Component Tests**: Storybook for component isolation

## Security Considerations

### Session Security

- **Secure Cookies**: Use httpOnly, secure, and sameSite cookies
- **Session Rotation**: Rotate session tokens on sensitive operations
- **Session Expiration**: Implement appropriate session timeouts
- **CSRF Protection**: Built-in CSRF protection with better-auth

### Password Security

- **Password Hashing**: Argon2 hashing (built into better-auth)
- **Password Policies**: Enforce strong password requirements
- **Rate Limiting**: Implement login attempt rate limiting
- **Account Lockout**: Temporary lockout after failed attempts

### Data Protection

- **Input Validation**: Validate all user inputs
- **SQL Injection**: Use parameterized queries (handled by ORM)
- **XSS Prevention**: Sanitize user-generated content
- **Environment Variables**: Secure storage of sensitive configuration

## Optional Client Plugin Configurations

### 1. Admin Client Plugin

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/react";
import { adminClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    adminClient(), // Enables admin functionality
  ],
});

// Admin hook for checking permissions
export const useAdminPermissions = () => {
  const { data: user } = useUser();

  const isAdmin = user?.role === "admin" || user?.role?.includes("admin");

  const checkPermission = async (permissions: Record<string, string[]>) => {
    if (!isAdmin) return false;

    const { data } = await authClient.admin.hasPermission({
      permissions,
    });
    return data;
  };

  return { isAdmin, checkPermission };
};
```

### 2. Passkey Client Plugin

```typescript
// lib/auth-client.ts
import { createAuthClient } from "better-auth/react";
import { passkeyClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    passkeyClient(), // Enables passkey authentication methods
  ],
});

// Usage in components
const handlePasskeySignIn = async () => {
  const { data, error } = await authClient.signIn.passkey();
  if (error) {
    console.error("Passkey sign in failed:", error);
  }
};
```

### 3. Two-Factor Authentication Client Plugin

```typescript
// lib/auth-client.ts
import { twoFactorClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    twoFactorClient(), // Enables 2FA methods
  ],
});

// Usage in components
const handleEnable2FA = async () => {
  const { data, error } = await authClient.twoFactor.enable();
  if (data) {
    // Show QR code for TOTP setup
    setQrCode(data.qrCode);
  }
};
```

### 4. Organization Client Plugin

```typescript
// lib/auth-client.ts
import { organizationClient } from "better-auth/client/plugins";

export const authClient = createAuthClient({
  baseURL: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  plugins: [
    organizationClient(), // Enables organization methods
  ],
});

// Usage in components
const handleCreateOrganization = async (name: string) => {
  const { data, error } = await authClient.organization.create({
    name,
    slug: name.toLowerCase().replace(/\s+/g, "-"),
  });
};
```

## Environment Configuration

### Client-Side Environment Variables

```env
# .env.development
NODE_ENV=development
REACT_APP_AUTH_URL=http://localhost:3001

# .env.production
NODE_ENV=production
REACT_APP_AUTH_URL=https://your-auth-server.com
```

### Environment-Based Configuration

```typescript
// lib/config.ts
export const config = {
  authUrl: process.env.REACT_APP_AUTH_URL || "http://localhost:3001",
  isDevelopment: process.env.NODE_ENV === "development",
  isProduction: process.env.NODE_ENV === "production",

  // Feature flags based on environment
  features: {
    protectedRoutes: process.env.NODE_ENV === "production",
    debugMode: process.env.NODE_ENV === "development",
  },
};
```

## Admin Access Control and Permissions

### Role-Based Access Control

```typescript
// lib/admin-permissions.ts
import { createAccessControl } from "better-auth/plugins/access";

// Define custom permissions for the broker platform
export const statement = {
  user: [
    "create",
    "list",
    "set-role",
    "ban",
    "impersonate",
    "delete",
    "set-password",
  ],
  session: ["list", "revoke", "delete"],
  listing: ["create", "update", "delete", "publish"],
  workspace: ["create", "update", "delete", "manage-members"],
  report: ["view", "export", "create"],
} as const;

const ac = createAccessControl(statement);

// Define roles with specific permissions
export const adminRole = ac.newRole({
  user: [
    "create",
    "list",
    "set-role",
    "ban",
    "impersonate",
    "delete",
    "set-password",
  ],
  session: ["list", "revoke", "delete"],
  listing: ["create", "update", "delete", "publish"],
  workspace: ["create", "update", "delete", "manage-members"],
  report: ["view", "export", "create"],
});

export const brokerRole = ac.newRole({
  user: ["list"],
  listing: ["create", "update", "delete", "publish"],
  workspace: ["update", "manage-members"],
  report: ["view", "export"],
});

export const agentRole = ac.newRole({
  listing: ["create", "update"],
  report: ["view"],
});

export const viewerRole = ac.newRole({
  listing: [],
  report: ["view"],
});

// Export access control configuration
export { ac };
```

### Admin Route Protection

```typescript
// components/admin/AdminRoute.tsx
import { useAdminPermissions } from "@/hooks/useAdminPermissions";

interface AdminRouteProps {
  children: React.ReactNode;
  requiredPermissions?: Record<string, string[]>;
  fallback?: React.ReactNode;
}

export const AdminRoute: React.FC<AdminRouteProps> = ({
  children,
  requiredPermissions,
  fallback = <div>Access denied. Admin privileges required.</div>,
}) => {
  const { isAdmin, checkPermission } = useAdminPermissions();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    const checkAccess = async () => {
      if (!isAdmin) {
        setHasPermission(false);
        return;
      }

      if (!requiredPermissions) {
        setHasPermission(true);
        return;
      }

      const permitted = await checkPermission(requiredPermissions);
      setHasPermission(permitted);
    };

    checkAccess();
  }, [isAdmin, requiredPermissions, checkPermission]);

  if (hasPermission === null) {
    return <div>Checking permissions...</div>;
  }

  if (!hasPermission) {
    return fallback;
  }

  return <>{children}</>;
};
```

### Permission-Based UI Components

```typescript
// components/admin/PermissionGate.tsx
interface PermissionGateProps {
  permissions: Record<string, string[]>;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  permissions,
  children,
  fallback = null,
}) => {
  const { checkPermission } = useAdminPermissions();
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  useEffect(() => {
    checkPermission(permissions).then(setHasPermission);
  }, [permissions, checkPermission]);

  if (hasPermission === null) {
    return <div>Loading...</div>;
  }

  return hasPermission ? <>{children}</> : <>{fallback}</>;
};

// Usage example
export const UserManagementSection = () => (
  <PermissionGate
    permissions={{ user: ["list", "create", "delete"] }}
    fallback={<div>You don't have permission to manage users.</div>}
  >
    <AdminUserManagement />
  </PermissionGate>
);
```

## Performance Considerations

### Client-Side Optimization

- **Code Splitting**: Lazy load authentication and admin components
- **Bundle Size**: Tree-shake unused better-auth features
- **Caching**: Cache user session data and permissions appropriately
- **Loading States**: Implement proper loading indicators for admin operations
- **Pagination**: Implement efficient pagination for user lists and session management
- **Permission Caching**: Cache permission checks to avoid repeated API calls

## Deployment Strategy

### Development Setup

1. Install better-auth client dependencies
2. Configure environment variables for auth server URL
3. Set up React development server
4. Ensure external auth server is running and accessible

### Production Deployment

1. Build React application with production environment variables
2. Deploy to static hosting (Vercel, Netlify, etc.)
3. Ensure production auth server URL is correctly configured
4. Test authentication flows in production environment
