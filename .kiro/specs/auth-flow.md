
- [ ] 1. Authentication & User Management APIs

### Auth APIs

#### POST /auth/signup
**Description:** User registration with workspace creation
**Input:**
```typescript
{
  email: string;
  password: string;
  confirmPassword: string;
  first_name: string;
  last_name: string;
  company_name: string;
  company_type: 'individual' | 'team' | 'firm';
  phone?: string;
  license_number?: string;
  website?: string;
  address?: string;
  terms_accepted: boolean;
  marketing_consent?: boolean;
}
```
**Output:**
```typescript
{
  user: User;
  workspace: Workspace;
  profile: UserProfile;
  session: AuthSession;
}
```

#### POST /auth/signin
**Description:** User authentication with optional workspace selection
**Input:**
```typescript
{
  email: string;
  password: string;
  workspace_id?: string;
  remember_me?: boolean;
}
```
**Output:**
```typescript
{
  user: User;
  session: AuthSession;
  workspace: Workspace;
  profile: UserProfile;
}
```

#### POST /auth/signout
**Description:** User logout
**Input:**
```typescript
{
  refresh_token: string;
}
```
**Output:**
```typescript
{
  success: boolean;
}
```

#### POST /auth/refresh
**Description:** Refresh authentication token
**Input:**
```typescript
{
  refresh_token: string;
}
```
**Output:**
```typescript
{
  access_token: string;
  refresh_token: string;
  expires_at: number;
}
```

#### POST /auth/forgot-password
**Description:** Password reset request
**Input:**
```typescript
{
  email: string;
}
```
**Output:**
```typescript
{
  success: boolean;
  message: string;
}
```

#### POST /auth/reset-password
**Description:** Password reset confirmation
**Input:**
```typescript
{
  token: string;
  new_password: string;
  confirm_password: string;
}
```
**Output:**
```typescript
{
  success: boolean;
}
```

#### GET /auth/callback
**Description:** Handle email verification callback
**Query Parameters:** Token and verification parameters from email link
**Output:** Redirect to dashboard or error page