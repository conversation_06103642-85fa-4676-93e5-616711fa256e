- [x] File Management APIs

> **Note on File Storage:** Files are stored in Supabase Storage using a structured path system: `{workspace_id}/{entity_type}/{entity_id}/{file_name}`. All file uploads include virus scanning, file type validation, and size restrictions. The centralized upload endpoint supports both multipart form data and resumable uploads for files larger than 6MB.

#### POST /files/upload

**Description:** Centralized file upload endpoint for all file types (avatars, documents, listing photos, etc.)
**Content-Type:** `multipart/form-data` or `application/offset+octet-stream`
**Input:**

```typescript
// Form Data Fields
{
  file: File; // The actual file being uploaded
  file_type: string; // Type/category of file (e.g., 'avatar', 'document', 'listing_photo')
  entity_type?: string; // Optional: 'user', 'listing', 'workspace' for association
  entity_id?: string; // Optional: UUID of the associated entity
  is_public?: boolean; // Default: false
}
```

**Output:**

```typescript
{
  success: boolean;
  file: {
    id: string;
    workspace_id: string;
    uploaded_by: string;
    file_name: string; // Generated unique filename stored in Supabase Storage
    original_name: string; // Original filename from upload
    mime_type: string;
    file_size: number; // Size in bytes
    storage_path: string; // Full path in Supabase Storage
    storage_url?: string; // Public URL if is_public=true
    file_type: string; // Type/category from input
    entity_type?: string; // Associated entity type (user, listing, workspace, etc.)
    entity_id?: string; // UUID of associated entity
    is_public: boolean;
    metadata: object; // JSON object for additional file metadata
    created_at: string;
    updated_at: string;
  };

  // Processing status for async operations
  processing?: {
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number; // 0-100
    estimated_completion?: string; // ISO timestamp
  };

}
```

#### GET /files/:file_id

**Description:** Get file metadata and download URL
**Output:**

```typescript
{
  id: string;
  workspace_id: string;
  uploaded_by: string;
  file_name: string;
  original_name: string;
  mime_type: string;
  file_size: number;
  storage_path: string;
  storage_url?: string; // Public URL if accessible
  signed_url?: string; // Temporary signed URL for private files (expires in 1 hour)
  file_type: string;
  entity_type?: string;
  entity_id?: string;
  is_public: boolean;
  metadata: object; // JSONB field from database
  created_at: string;
  updated_at: string;

  // Access permissions (computed fields)
  can_download: boolean;
  can_delete: boolean;
  can_update: boolean;
}
```

#### DELETE /files/:file_id

**Description:** Delete a file and its storage data
**Output:**

```typescript
{
  success: boolean;
  message: string;
  deleted_file: {
    id: string;
    file_name: string;
    storage_path: string;
  }
}
```
