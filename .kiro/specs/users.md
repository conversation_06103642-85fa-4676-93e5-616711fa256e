
- [ ] User Profile APIs

Check first on what have been implemented, then execute implementing the rest

> **Note on Avatar Uploads:** User avatars should be uploaded using the centralized `POST /files/upload` endpoint (see File Management APIs section) with `type=avatar` and `resource_type=user`. The returned `file_id` is then used in the `avatar_file_id` field when updating the user profile via `PUT /users/profile`.

#### GET /users/profile
**Description:** Get current user profile with workspace context
**Output:**
```typescript
{
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  invited_at?: string;
  joined_at?: string;
  invited_by?: string;
  preferences: {
    notifications: {
      email_notifications: boolean;
      push_notifications: boolean;
      listing_updates: boolean;
      team_updates: boolean;
      system_updates: boolean;
    };
    display: {
      timezone: string;
      date_format: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
      currency: string;
      language: string;
    };
    privacy: {
      profile_visibility: 'public' | 'team' | 'private';
      contact_visibility: 'public' | 'team' | 'private';
    };
  };
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}
```

#### PUT /users/profile
**Description:** Update user profile information (centralized endpoint for all profile updates)
**Input:**
```typescript
{
  // Basic Information
  first_name?: string;
  last_name?: string;
  phone?: string;
  license_number?: string;
  bio?: string;
  specialties?: string[];
  avatar_file_id?: string; // File ID from POST /files upload
  
  // Security Updates
  current_password?: string; // Required for password/email changes
  new_password?: string;
  confirm_password?: string;
  new_email?: string;
  
  // Preferences
  preferences?: {
    notifications?: {
      email_notifications?: boolean;
      push_notifications?: boolean;
      listing_updates?: boolean;
      team_updates?: boolean;
      system_updates?: boolean;
    };
    display?: {
      timezone?: string;
      date_format?: 'MM/DD/YYYY' | 'DD/MM/YYYY' | 'YYYY-MM-DD';
      currency?: string;
      language?: string;
    };
    privacy?: {
      profile_visibility?: 'public' | 'team' | 'private';
      contact_visibility?: 'public' | 'team' | 'private';
    };
  }
}
```
**Output:**
```typescript
{
  id: string;
  workspace_id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  phone?: string;
  license_number?: string;
  bio?: string;
  avatar_url?: string;
  specialties: string[];
  is_active: boolean;
  preferences: UserPreferences;
  updated_at: string;
  
  // Response metadata for operations
  operations_completed: {
    profile_updated: boolean;
    password_changed?: boolean;
    email_change_initiated?: boolean;
    email_verification_sent?: boolean;
    avatar_updated?: boolean;
  };
  
  // Warnings or next steps
  requires_reauth?: boolean;
  warnings?: string[];
}
```

#### POST /users/verify-email
**Description:** Verify new email address (called after email change via PUT /users/profile)
**Input:**
```typescript
{
  token: string;
  email: string;
}
```
**Output:**
```typescript
{
  success: boolean;
  message: string;
  email_updated: boolean;
}
```
