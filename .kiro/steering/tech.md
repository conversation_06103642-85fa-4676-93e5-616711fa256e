# Technology Stack

## Runtime & Build System

- **Runtime**: Bun (JavaScript/TypeScript runtime and package manager)
- **Build Target**: Bun with sourcemap support
- **Package Manager**: Bun (with npm fallback via package-lock.json)

## Core Framework & Libraries

- **Web Framework**: Hono.js - Ultrafast web framework
- **API Documentation**: @hono/zod-openapi for OpenAPI 3.1 spec generation
- **Database ORM**: Drizzle ORM with PostgreSQL
- **Database**: PostgreSQL via Supabase
- **Authentication**: Supabase Auth with JWT tokens
- **Validation**: Zod for runtime type validation and schema definition
- **Logging**: Pino with hono-pino middleware
- **Testing**: Vitest
- **Linting**: ESLint with @antfu/eslint-config

## Key Dependencies

- **@scalar/hono-api-reference**: Interactive API documentation UI
- **drizzle-zod**: Bridge between Drizzle schema and Zod validation
- **papaparse**: CSV parsing for business listing imports
- **openai**: AI integration for listing enhancement
- **uuid**: UUID generation for primary keys
- **stoker**: Hono utilities and helpers

## Development Tools

- **TypeScript**: Full type safety with strict configuration
- **Hot Reload**: Bun's built-in hot reload for development
- **Cross-env**: Environment variable management across platforms
- **tsx**: TypeScript execution for scripts

## Common Commands

```bash
# Development
bun run dev              # Start development server with hot reload
bun run build            # Build for production
bun run start            # Start production server

# Database Operations
bun run db:generate      # Generate Drizzle migrations
bun run db:migrate       # Run database migrations
bun run db:push          # Push schema directly to database
bun run db:pull          # Pull schema from database

# Code Quality
bun run typecheck        # TypeScript type checking
bun run lint             # Run ESLint
bun run lint:fix         # Fix ESLint issues automatically

# Testing
bun run test             # Run test suite
bun run test --watch     # Run tests in watch mode

# API Documentation
bun run api:generate-types  # Generate TypeScript types from OpenAPI spec
```

## Environment Configuration

- **Development**: `.env` file with hot reload
- **Testing**: `.env.test` file for test environment
- **Production**: Environment variables with validation via Zod schema

## Architecture Patterns

- **OpenAPI-First**: All routes defined with OpenAPI schemas
- **Type-Safe**: End-to-end TypeScript with runtime validation
- **Middleware-Based**: Composable authentication, validation, and logging
- **Multi-tenant**: Workspace isolation at database and application level