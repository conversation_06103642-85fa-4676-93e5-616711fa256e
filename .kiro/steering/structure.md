# Project Structure

## Root Directory

```
├── src/                    # Source code
├── dist/                   # Build output
├── docs/                   # Documentation
├── public/                 # Static files (CSV samples)
├── .kiro/                  # Kiro configuration and specs
├── node_modules/           # Dependencies
├── package.json            # Project configuration
├── bun.lockb              # Bun lock file
├── drizzle.config.ts      # Database configuration
├── tsconfig.json          # TypeScript configuration
├── vitest.config.ts       # Test configuration
└── eslint.config.mjs      # ESLint configuration
```

## Source Code Organization (`src/`)

### Core Application Files
- `index.ts` - Server entry point
- `app.ts` - Main application setup and route registration
- `env.ts` - Environment variable validation and configuration

### Database Layer (`src/db/`)
- `index.ts` - Database connection and client setup
- `schema.ts` - Drizzle ORM schema definitions
- `migrations/` - Database migration files and metadata

### Library Code (`src/lib/`)
- `create-app.ts` - Hono app factory with middleware setup
- `configure-open-api.ts` - OpenAPI documentation configuration
- `auth-utils.ts` - Authentication helper functions
- `supabase.ts` - Supabase client configuration
- `openai.ts` - OpenAI integration
- `csv-parser.ts` - CSV parsing utilities
- `types.ts` - Shared TypeScript types
- `constants.ts` - Application constants
- `cors.ts` - CORS configuration

### Middleware (`src/middlewares/`)
- `auth.ts` - Authentication and authorization middleware
- `validation.ts` - Request validation middleware
- `error-handler.ts` - Global error handling
- `pino-logger.ts` - Request logging middleware
- `api-logger.ts` - API request logging

### Routes (`src/routes/`)
Routes are organized by API version and resource:

```
src/routes/
├── index.route.ts          # Root routes
├── api-client.route.ts     # API client routes
└── v1/                     # API version 1
    ├── auth/               # Authentication endpoints
    ├── users/              # User management
    ├── files/              # File upload/management
    ├── listings/           # Business listings
    ├── logs/               # API logging endpoints
    └── workspaces/         # Workspace management
```

### Route Module Pattern
Each route module follows this structure:
- `*.routes.ts` - Route definitions with OpenAPI schemas
- `*.controller.ts` - Route handlers and business logic
- `*.service.ts` - Data access and business services
- `*.test.ts` - Unit and integration tests

### Types (`src/types/`)
- `api.ts` - Auto-generated API types from OpenAPI spec

## Configuration Files

### Environment Files
- `.env` - Development environment variables
- `.env.example` - Environment template
- `.env.test` - Test environment configuration

### Database Configuration
- `drizzle.config.ts` - Drizzle ORM configuration
- Database migrations in `src/db/migrations/`

### Development Tools
- `tsconfig.json` - TypeScript compiler options
- `eslint.config.mjs` - ESLint rules and configuration
- `vitest.config.ts` - Test runner configuration

## Naming Conventions

### Files and Directories
- Use kebab-case for file names: `auth-utils.ts`
- Use camelCase for TypeScript files when appropriate: `createApp.ts`
- Route files end with `.routes.ts`
- Test files end with `.test.ts`
- Service files end with `.service.ts`

### Database Schema
- Table names use snake_case: `user_profiles`, `listing_notes`
- Column names use snake_case: `created_at`, `workspace_id`
- Primary keys are UUIDs with `defaultRandom()`
- Foreign keys follow pattern: `{table}_id`

### API Routes
- RESTful resource naming: `/v1/users`, `/v1/listings`
- Nested resources: `/v1/workspaces/:id/users`
- Use plural nouns for collections
- Use HTTP verbs appropriately (GET, POST, PUT, DELETE)

## Import Patterns

### Path Aliases
- `@/` maps to `src/` directory
- Use absolute imports: `import { authMiddleware } from '@/middlewares/auth'`
- Avoid relative imports beyond one level

### Module Exports
- Use named exports for utilities and services
- Use default exports for main application components
- Export types alongside implementations when relevant

## Testing Structure

- Tests are co-located with source files
- Use `.test.ts` suffix for test files
- Integration tests for route handlers
- Unit tests for services and utilities
- Mock external dependencies (Supabase, OpenAI)