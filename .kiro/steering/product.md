# Product Overview

This is a multi-tenant business brokerage platform built as a REST API with OpenAPI documentation. The platform enables business brokers and their teams to manage business listings, client relationships, and workspace collaboration.

## Core Features

- **Multi-tenant Architecture**: Workspace-based isolation with role-based access control
- **Business Listings Management**: Create, manage, and track business sale listings with detailed financial information
- **User & Team Management**: Invite team members, manage roles, and control access permissions
- **File Management**: Upload and organize documents, photos, and other assets
- **Activity Logging**: Comprehensive API request logging and audit trails
- **Authentication**: Supabase-based JWT authentication with workspace context

## Key Entities

- **Workspaces**: Company/team containers with subscription management
- **Users**: Authenticated users with profiles and role assignments
- **Listings**: Business sale listings with financial details and status tracking
- **Files**: Document and media management with workspace isolation
- **Notifications**: In-app notification system for team communication

## Business Domain

The platform serves business brokers who help facilitate the sale of businesses. It handles confidential business information, financial data, and multi-party transactions requiring careful access control and data privacy.