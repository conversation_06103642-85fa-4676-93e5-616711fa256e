import { cors } from "hono/cors";
import type { MiddlewareHandler } from "hono";
import env from "@/env";

export interface CorsConfig {
  origins?: string[];
  allowHeaders?: string[];
  allowMethods?: string[];
  credentials?: boolean;
  maxAge?: number;
}

/**
 * Default CORS configuration for the application
 */
const defaultCorsConfig = {
  origins: env.CORS_ORIGINS,
  allowHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'x-api-key'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  maxAge: env.CORS_MAX_AGE,
};

/**
 * Create CORS middleware with custom or default configuration
 */
export function createCorsMiddleware(config?: Partial<CorsConfig>): MiddlewareHandler {
  const origins = config?.origins ?? defaultCorsConfig.origins;
  const allowHeaders = config?.allowHeaders ?? defaultCorsConfig.allowHeaders;
  const allowMethods = config?.allowMethods ?? defaultCorsConfig.allowMethods;
  const credentials = config?.credentials ?? defaultCorsConfig.credentials;
  const maxAge = config?.maxAge ?? defaultCorsConfig.maxAge;
  
  return cors({
    origin: origins,
    allowHeaders: allowHeaders,
    allowMethods: allowMethods,
    credentials: credentials,
    maxAge: maxAge,
  });
}

/**
 * Standard CORS middleware for API routes
 */
export const apiCors = createCorsMiddleware();

/**
 * Permissive CORS middleware for public endpoints
 */
export const publicCors = createCorsMiddleware({
  origins: ['*'],
  credentials: false,
  allowHeaders: [
    'Content-Type', 
    'Authorization', 
    'X-Requested-With',
    'Accept',
    'Origin',
    'x-api-key'
  ],
});

/**
 * Environment-aware CORS middleware
 */
export function createEnvironmentCors(): MiddlewareHandler {
  const isProduction = env.NODE_ENV === 'production';
  
  if (isProduction) {
    // In production, use environment-configured origins
    return createCorsMiddleware({
      origins: env.CORS_ORIGINS,
      credentials: true,
      maxAge: env.CORS_MAX_AGE,
    });
  }
  
  // In development, use environment-configured settings (same as default)
  return apiCors;
} 