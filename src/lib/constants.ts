import * as HttpStatusPhrases from "stoker/http-status-phrases";
import { createMessageObjectSchema } from "stoker/openapi/schemas";

export const ZOD_ERROR_MESSAGES = {
  REQUIRED: "Required",
  EXPECTED_NUMBER: "Expected number, received nan",
  NO_UPDATES: "No updates provided",
};

export const ZOD_ERROR_CODES = {
  INVALID_UPDATES: "invalid_updates",
};

// Register common error schemas as OpenAPI components
export const notFoundSchema = createMessageObjectSchema(HttpStatusPhrases.NOT_FOUND).openapi("NotFoundError");
export const badRequestSchema = createMessageObjectSchema(HttpStatusPhrases.BAD_REQUEST).openapi("BadRequestError");
export const unauthorizedSchema = createMessageObjectSchema(HttpStatusPhrases.UNAUTHORIZED).openapi("UnauthorizedError");
export const forbiddenSchema = createMessageObjectSchema(HttpStatusPhrases.FORBIDDEN).openapi("ForbiddenError");
export const internalServerErrorSchema = createMessageObjectSchema(HttpStatusPhrases.INTERNAL_SERVER_ERROR).openapi("InternalServerError");
