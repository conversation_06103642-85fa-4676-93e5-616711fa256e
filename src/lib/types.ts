import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, RouteConfig, RouteH<PERSON><PERSON> } from "@hono/zod-openapi";
import type { Schema } from "hono";
import type { <PERSON><PERSON>Log<PERSON> } from "hono-pino";
import type { AuthenticatedUser } from "@/middlewares/auth";

export interface AppBindings {
  Variables: {
    logger: PinoLogger;
    user?: AuthenticatedUser;
    validatedBody?: any;
    validatedQuery?: any;
    validatedParams?: any;
    uploadedFile?: File;
  };
};

// eslint-disable-next-line ts/no-empty-object-type
export type AppOpenAPI<S extends Schema = {}> = OpenAPIHono<AppBindings, S>;

export type AppRouteHandler<R extends RouteConfig> = RouteHandler<R, AppBindings>;
