
import { z } from "zod";


// =============================================================================
// LISTINGS SCHEMAS (moved to src/routes/v1/listings/listings.routes.ts)
// =============================================================================
// All listings-related schemas have been moved to the listings.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.

// =============================================================================
// USER PROFILE SCHEMAS (moved to src/routes/v1/users/users.routes.ts)
// =============================================================================
// All user profile-related schemas have been moved to the users.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.

// =============================================================================
// WORKSPACE SCHEMAS (moved to src/routes/v1/workspaces/workspaces.routes.ts)
// =============================================================================
// All workspace and workspace invitation schemas have been moved to the workspaces.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.

// =============================================================================
// FILES SCHEMAS (moved to src/routes/v1/files/files.routes.ts)
// =============================================================================
// All files-related schemas have been moved to the files.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.

// =============================================================================
// API LOGS SCHEMAS (moved to src/routes/v1/logs/logs.routes.ts)
// =============================================================================
// All logs-related schemas have been moved to the logs.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.

// =============================================================================
// NOTIFICATIONS SCHEMAS
// =============================================================================

// export const insertNotificationSchema = createInsertSchema(notifications, {
//   type: z.string().min(1),
//   title: z.string().min(1),
//   message: z.string().min(1),
//   priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
//   isRead: z.boolean().default(false),
// }).openapi("InsertNotification");

// export const selectNotificationSchema = createSelectSchema(notifications).openapi("Notification");

export const insertNotificationSchema = z.object({
  workspaceId: z.string().uuid(),
  userId: z.string().uuid(),
  type: z.string().min(1),
  title: z.string().min(1),
  message: z.string().min(1),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  isRead: z.boolean().default(false),
  readAt: z.string().datetime().optional(),
}).openapi("InsertNotification");

export const selectNotificationSchema = z.object({
  id: z.string().uuid(),
  workspaceId: z.string().uuid(),
  userId: z.string().uuid(),
  type: z.string(),
  title: z.string(),
  message: z.string(),
  priority: z.string(),
  isRead: z.boolean(),
  readAt: z.string().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
}).openapi("Notification");

export const createNotificationRequestSchema = insertNotificationSchema
  .openapi("CreateNotificationRequest");

export const updateNotificationRequestSchema = z.object({
  isRead: z.boolean().optional(),
  readAt: z.string().datetime().optional(),
}).openapi("UpdateNotificationRequest");

export const notificationResponseSchema = selectNotificationSchema
  .openapi("Notification");

export const notificationListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(notificationResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
  unreadCount: z.number().optional(),
}).openapi("NotificationListResponse");

// =============================================================================
// AUTH SCHEMAS (moved to src/routes/v1/auth/auth.routes.ts)
// =============================================================================
// All auth-related schemas have been moved to the auth.routes.ts file
// to follow DRY principles and keep schemas co-located with their routes.



// =============================================================================
// COMMON RESPONSE WRAPPERS
// =============================================================================

export const successResponseSchema = z.object({
  success: z.boolean(),
}).openapi("SuccessResponse");

export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  details: z.any().optional(),
}).openapi("ErrorResponse");

export const deleteResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  deletedId: z.string().uuid(),
  deletedAt: z.string(),
}).openapi("DeleteResponse");

export const paginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  pages: z.number(),
}).openapi("Pagination");

// =============================================================================
// BATCH/BULK UPDATE SCHEMAS
// =============================================================================

export const bulkUpdateRequestSchema = z.object({
  ids: z.array(z.string().uuid()).min(1).max(100),
  updates: z.record(z.any()),
}).openapi("BulkUpdateRequest");

export const bulkUpdateResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    updated: z.array(z.string().uuid()),
    failed: z.array(z.object({
      id: z.string().uuid(),
      error: z.string(),
    })),
    total: z.number(),
    updatedCount: z.number(),
    failedCount: z.number(),
  }),
}).openapi("BulkUpdateResponse");

export const bulkDeleteRequestSchema = z.object({
  ids: z.array(z.string().uuid()).min(1).max(100),
  reason: z.string().optional(),
}).openapi("BulkDeleteRequest");

export const bulkDeleteResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    deleted: z.array(z.string().uuid()),
    failed: z.array(z.object({
      id: z.string().uuid(),
      error: z.string(),
    })),
    total: z.number(),
    deletedCount: z.number(),
    failedCount: z.number(),
  }),
}).openapi("BulkDeleteResponse");

// =============================================================================
// GENERIC UPDATE RESPONSE WRAPPERS
// =============================================================================

// For single resource updates
export const createUpdateResponseWrapper = <T extends z.ZodTypeAny>(schema: T, name: string) => {
  return z.object({
    success: z.boolean(),
    data: schema,
    updatedAt: z.string(),
    changesApplied: z.array(z.string()).optional(),
  }).openapi(`${name}UpdateResponse`);
};

// For list responses with metadata
export const createListResponseWrapper = <T extends z.ZodTypeAny>(schema: T, name: string) => {
  return z.object({
    success: z.boolean(),
    data: z.array(schema),
    pagination: paginationSchema,
    totalCount: z.number().optional(),
    filteredCount: z.number().optional(),
  }).openapi(`${name}ListResponse`);
};

 