import env from "@/env";

/**
 * Get secure cookie options for refresh tokens
 */
export function getRefreshTokenCookieOptions() {
  return {
    httpOnly: true,
    secure: env.NODE_ENV === 'production', // Only secure in production
    sameSite: 'Lax' as const,
    maxAge: 60 * 60 * 24 * 30, // 30 days
    path: '/'
  };
}

/**
 * Cookie names used in the application
 */
export const COOKIE_NAMES = {
  REFRESH_TOKEN: 'refresh_token'
} as const;