import type { Context } from "hono";
import { LogsService, type LogData } from "@/routes/v1/logs/logs.service";

// Re-export for backward compatibility
export type { LogData };

export class ApiLogger {
  // Lightweight in-memory queue to decouple logging from request lifecycle
  private static queue: LogData[] = [];
  private static flushing = false;
  private static flushScheduled = false;
  private static readonly maxBatchSize = 25;
  private static readonly scheduleDelayMs = 25;

  /**
   * Log API request/response to database
   */
  static log(data: LogData): void {
    // Enqueue and schedule a background flush
    this.queue.push(data);
    this.scheduleFlush();
  }

  /**
   * Schedule a background flush if not already scheduled
   */
  private static scheduleFlush(): void {
    if (this.flushScheduled || this.flushing) return;
    this.flushScheduled = true;
    setTimeout(() => {
      this.flushScheduled = false;
      // Run flush without blocking the caller
      void this.flush();
    }, this.scheduleDelayMs);
  }

  /**
   * Flush queued logs in batches with limited concurrency
   */
  private static async flush(): Promise<void> {
    if (this.flushing) return;
    this.flushing = true;
    try {
      while (this.queue.length > 0) {
        const batch = this.queue.splice(0, this.maxBatchSize);
        // Persist without throwing; failures are swallowed to avoid feedback loops
        await Promise.allSettled(batch.map((entry) => LogsService.addLog(entry)));
      }
    } finally {
      this.flushing = false;
      // If new items arrived during flush, schedule another round
      if (this.queue.length > 0) this.scheduleFlush();
    }
  }

  /**
   * Get IP address from request
   */
  static getIpAddress(c: Context): string {
    // Check various headers for IP address
    const forwarded = c.req.header('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIp = c.req.header('x-real-ip');
    if (realIp) {
      return realIp;
    }
    
    const cfConnectingIp = c.req.header('cf-connecting-ip');
    if (cfConnectingIp) {
      return cfConnectingIp;
    }
    
    // Fallback to connection remote address
    return 'unknown';
  }

  /**
   * Sanitize object by removing sensitive fields
   */
  private static sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sensitiveFields = [
      'password', 
      'confirmPassword', 
      'currentPassword', 
      'newPassword',
      'token', 
      'accessToken', 
      'refreshToken',
      'secret',
      'apiKey',
      'privateKey',
      'ssn',
      'socialSecurityNumber',
      'creditCard',
      'cardNumber',
      'cvv',
      'pin'
    ];

    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const lowerKey = key.toLowerCase();
      if (sensitiveFields.some(field => lowerKey.includes(field))) {
        result[key] = '[REDACTED]';
      } else {
        result[key] = this.sanitizeObject(value);
      }
    }
    return result;
  }

  /**
   * Sanitize request body for logging (remove sensitive data)
   */
  static sanitizeRequestBody(body: any, path: string): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    return this.sanitizeObject({ ...body });
  }

  /**
   * Sanitize response body for logging
   */
  static sanitizeResponseBody(body: any, statusCode: number): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    // Don't log large successful responses (like file downloads)
    if (statusCode >= 200 && statusCode < 300 && typeof body === 'object') {
      const bodyStr = JSON.stringify(body);
      if (bodyStr.length > 50000) { // 50KB limit
        return { 
          message: '[RESPONSE TOO LARGE]',
          size: bodyStr.length,
          type: Array.isArray(body) ? 'array' : 'object'
        };
      }
    }

    // For error responses and other cases, sanitize sensitive data
    return this.sanitizeObject(body);
  }

  /**
   * Check if path should be logged
   */
  static shouldLog(path: string, method: string): boolean {
    // Skip logging for certain paths
    const skipPaths = [
      '/health',
      '/doc',
      '/reference',
      '/favicon.ico',
      '/_log' // Don't log requests to the logging endpoints themselves
    ];

    // Skip OPTIONS requests (CORS preflight)
    if (method === 'OPTIONS') {
      return false;
    }

    return !skipPaths.some(skipPath => path.startsWith(skipPath));
  }
} 