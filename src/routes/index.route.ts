import { createRoute } from "@hono/zod-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { jsonContent } from "stoker/openapi/helpers";
import { createMessageObjectSchema } from "stoker/openapi/schemas";
import { z } from "@hono/zod-openapi";

import { createRouter } from "@/lib/create-app";

// Health check response schema
const healthCheckSchema = z.object({
  status: z.string(),
  timestamp: z.string(),
  uptime: z.number(),
  version: z.string(),
}).openapi("HealthCheck");

// Index response schema
const indexResponseSchema = z.object({
  message: z.string(),
  version: z.string(),
  documentation: z.string(),
  endpoints: z.object({
    health: z.string(),
    docs: z.string(),
    reference: z.string(),
  }),
}).openapi("ApiIndex");

const router = createRouter()
  .openapi(
    createRoute({
      tags: ["Index"],
      method: "get",
      path: "/",
      summary: "API Index",
      description: "Returns API information and available endpoints",
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          indexResponseSchema,
          "API Index Information",
        ),
      },
    }),
    (c) => {
      return c.json({
        message: "Welcome to Tasks API",
        version: "1.0.0",
        documentation: "Visit /reference for API documentation",
        endpoints: {
          health: "/health",
          docs: "/doc", 
          reference: "/reference",
        },
      }, HttpStatusCodes.OK);
    },
  )
  .openapi(
    createRoute({
      tags: ["Health"],
      method: "get",
      path: "/health",
      summary: "Health Check",
      description: "Returns the health status of the API",
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          healthCheckSchema,
          "Health Check Response",
        ),
      },
    }),
    (c) => {
      const uptime = process.uptime();
      
      return c.json({
        status: "healthy",
        timestamp: new Date().toISOString(),
        uptime: Math.floor(uptime),
        version: "1.0.0",
      }, HttpStatusCodes.OK);
    },
  );

export default router;
