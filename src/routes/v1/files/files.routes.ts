import { createRoute, z } from "@hono/zod-openapi";
import { successResponseSchema, deleteResponseSchema } from "@/lib/commonApiSchema";

// =============================================================================
// BASE FILES SCHEMAS
// =============================================================================

// Base file schema mapped from database structure
const baseFileSchema = z.object({
  workspaceId: z.string().uuid(),
  uploadedBy: z.string().uuid(),
  fileName: z.string().min(1),
  originalName: z.string().min(1),
  mimeType: z.string().min(1),
  fileSize: z.number().int().positive(),
  fileType: z.enum(['document', 'image', 'video', 'audio', 'other']),
  storagePath: z.string(),
  entityType: z.string().optional(),
  entityId: z.string().uuid().optional(),
  isPublic: z.boolean().default(false),
  metadata: z.record(z.any()).optional(),
}).openapi("BaseFile");

// Base file type enum for reuse
const fileTypeEnum = z.enum(['document', 'image', 'video', 'audio', 'other']);

// =============================================================================
// REQUEST SCHEMAS (reducing from base for API operations)
// =============================================================================

// Upload file request schema - only user-controllable fields
export const uploadFileRequestSchema = z.object({
  fileType: fileTypeEnum,
  entityType: z.string().optional(),
  entityId: z.string().uuid().optional(),
  isPublic: z.boolean().optional().default(false),
}).openapi("UploadFileRequest");

// Update file request schema - editable fields after upload
const updateFileRequestSchema = z.object({
  fileName: z.string().min(1).optional(),
  fileType: fileTypeEnum.optional(),
  entityType: z.string().optional(),
  entityId: z.string().uuid().optional(),
  isPublic: z.boolean().optional(),
  metadata: z.record(z.any()).optional(),
}).openapi("UpdateFileRequest");

// =============================================================================
// RESPONSE SCHEMAS (extending base with computed fields)
// =============================================================================

// File response schema - base schema + computed fields
export const fileResponseSchema = baseFileSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    createdAt: z.string(),
    updatedAt: z.string(),
    
    // Computed/permission fields
    signedUrl: z.string().optional(),
    canDownload: z.boolean().optional(),
    canDelete: z.boolean().optional(),
    canUpdate: z.boolean().optional(),
  })
  .openapi("File");

// =============================================================================
// SPECIALIZED RESPONSE SCHEMAS
// =============================================================================

// Processing status schema for file operations
const processingStatusSchema = z.object({
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  progress: z.number().min(0).max(100).optional(),
  estimatedCompletion: z.string().optional(),
}).openapi("ProcessingStatus");

// Upload response schema with processing info
const uploadFileResponseSchema = z.object({
  success: z.boolean(),
  file: fileResponseSchema,
  processing: processingStatusSchema.optional(),
}).openapi("UploadFileResponse");

// Get file response schema
const getFileResponseSchema = fileResponseSchema.openapi("GetFileResponse");

// =============================================================================
// REUSABLE RESPONSE WRAPPERS (for future use)
// =============================================================================

const fileUpdateResponseSchema = z.object({
  success: z.boolean(),
  data: fileResponseSchema,
  updatedAt: z.string(),
  changesApplied: z.array(z.string()).optional(),
}).openapi("FileUpdateResponse");

const fileListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(fileResponseSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    pages: z.number(),
  }),
  totalSize: z.number().optional(),
  totalSizeFormatted: z.string().optional(),
}).openapi("FileListResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// Routes
export const uploadFileRoute = createRoute({
  method: "post",
  path: "/v1/files/upload",
  request: {
    body: {
      content: {
        "multipart/form-data": {
          schema: uploadFileRequestSchema.extend({
            file: z.any().describe("The file to upload"),
          }).openapi("UploadFileFormData"),
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: uploadFileResponseSchema,
        },
      },
      description: "File uploaded successfully",
    },
    400: {
      description: "Bad request - validation errors or file processing failed",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    413: {
      description: "Payload too large - file size exceeds limit",
    },
  },
  tags: ["Files"],
});

export const getFileRoute = createRoute({
  method: "get",
  path: "/v1/files/{file_id}",
  request: {
    params: z.object({
      fileId: z.string().uuid(),
    }).openapi("GetFileParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: getFileResponseSchema,
        },
      },
      description: "File metadata and download URL",
    },
    404: {
      description: "File not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Files"],
});

export const deleteFileRoute = createRoute({
  method: "delete",
  path: "/v1/files/{file_id}",
  request: {
    params: z.object({
      fileId: z.string().uuid(),
    }).openapi("DeleteFileParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteResponseSchema,
        },
      },
      description: "File deleted successfully",
    },
    404: {
      description: "File not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Files"],
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./files.controller";

export const filesRouter = createRouter()
  .openapi(uploadFileRoute, handlers.uploadFile)
  .openapi(getFileRoute, handlers.getFile)
  .openapi(deleteFileRoute, handlers.deleteFile);

// Export router as default for backward compatibility during migration
export default filesRouter;