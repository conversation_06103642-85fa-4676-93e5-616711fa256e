import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/types";
import { HTTPException } from "hono/http-exception";
import { createErrorResponse } from "@/middlewares/validation";
import { getCurrentUser, getCurrentWorkspace } from "@/middlewares/auth";
import { FilesService } from "./files.service";
import type { 
  uploadFileRoute,
  getFileRoute,
  deleteFileRoute
} from "./files.routes";

export const uploadFile: AppRouteHandler<typeof uploadFileRoute> = async (c) => {
  try {
    // Get authenticated user and workspace from context
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);

    // Parse multipart form data
    const body = await c.req.parseBody();
    const file = body.file as File;
    const file_type = body.file_type as string;
    const entity_type = body.entity_type as string | undefined;
    const entity_id = body.entity_id as string | undefined;
    const is_public = body.is_public === 'true';

    if (!file) {
      return c.json(createErrorResponse(
        'FILE_REQUIRED',
        'File is required',
        c.req.path
      ), 400);
    }

    if (!file_type) {
      return c.json(createErrorResponse(
        'FILE_TYPE_REQUIRED',
        'File type is required',
        c.req.path
      ), 400);
    }

    const result = await FilesService.uploadFile({
      file,
      file_type,
      entity_type,
      entity_id,
      is_public,
      workspace_id: workspace.id,
      uploaded_by: user.id,
    });

    return c.json(result, 201);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.status === 413) {
        return c.json(createErrorResponse(
          'FILE_TOO_LARGE',
          error.message,
          c.req.path
        ), 413);
      }
      if (error.status === 400 && error.message.includes('File type')) {
        return c.json(createErrorResponse(
          'INVALID_FILE_TYPE',
          error.message,
          c.req.path
        ), 400);
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'UPLOAD_FAILED',
      error.message || 'File upload failed',
      c.req.path
    ), 400);
  }
};

export const getFile: AppRouteHandler<typeof getFileRoute> = async (c) => {
  try {
    // Get authenticated user and workspace from context
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);

    const { file_id } = c.req.valid("param");

    const result = await FilesService.getFile(file_id, workspace.id, user.id);
    return c.json(result);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.status === 404) {
        return c.json(createErrorResponse(
          'FILE_NOT_FOUND',
          'File not found',
          c.req.path
        ), 404);
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'GET_FILE_FAILED',
      error.message || 'Failed to retrieve file',
      c.req.path
    ), 500);
  }
};

export const deleteFile: AppRouteHandler<typeof deleteFileRoute> = async (c) => {
  try {
    // Get authenticated user and workspace from context
    const user = getCurrentUser(c);
    const workspace = getCurrentWorkspace(c);

    const { file_id } = c.req.valid("param");

    const result = await FilesService.deleteFile(file_id, workspace.id, user.id);
    return c.json(result);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      if (error.status === 404) {
        return c.json(createErrorResponse(
          'FILE_NOT_FOUND',
          'File not found',
          c.req.path
        ), 404);
      }
      if (error.status === 403) {
        return c.json(createErrorResponse(
          'INSUFFICIENT_PERMISSIONS',
          'Insufficient permissions to delete this file',
          c.req.path
        ), 403);
      }
      throw error;
    }
    return c.json(createErrorResponse(
      'DELETE_FILE_FAILED',
      error.message || 'Failed to delete file',
      c.req.path
    ), 500);
  }
};