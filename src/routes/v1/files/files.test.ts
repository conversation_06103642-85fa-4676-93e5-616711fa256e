import { describe, it, expect } from "vitest";
import { createTestApp } from "@/lib/create-app";
import filesModule from "./files.module";

describe("Files API - Integration Tests", () => {
  const app = createTestApp(filesModule as any);

  describe("Endpoint Availability", () => {
    it("should have upload endpoint that requires authentication", async () => {
      const formData = new FormData();
      formData.append("file", new File(["test"], "test.txt", { type: "text/plain" }));
      formData.append("file_type", "document");

      const res = await app.request("/v1/files/upload", {
        method: "POST",
        body: formData,
      });

      // Should not return 404 (endpoint exists)
      expect(res.status).not.toBe(404);
      
      // Should return 401 for missing authentication
      expect(res.status).toBe(401);
    });

    it("should have get file endpoint that requires authentication", async () => {
      const res = await app.request("/v1/files/123e4567-e89b-12d3-a456-************", {
        method: "GET",
      });

      // Should not return 404 (endpoint exists)
      expect(res.status).not.toBe(404);
      
      // Should return 401 for missing authentication
      expect(res.status).toBe(401);
    });

    it("should have delete file endpoint that requires authentication", async () => {
      const res = await app.request("/v1/files/123e4567-e89b-12d3-a456-************", {
        method: "DELETE",
      });

      // Should not return 404 (endpoint exists)
      expect(res.status).not.toBe(404);
      
      // Should return 401 for missing authentication
      expect(res.status).toBe(401);
    });

    it("should reject invalid UUID format in file ID", async () => {
      const res = await app.request("/v1/files/invalid-uuid", {
        method: "GET",
        headers: {
          "Authorization": "Bearer dummy-token"
        }
      });

      // Should return 422 for invalid UUID format (validation happens before auth)
      expect(res.status).toBe(422);
    });
  });
});