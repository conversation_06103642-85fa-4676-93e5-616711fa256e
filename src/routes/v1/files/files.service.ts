import { HTTPException } from "hono/http-exception";
import db from "@/db";
import { files } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { v4 as uuidv4 } from "uuid";
import fs from "node:fs";
import path from "node:path";

export interface UploadFileData {
  file: File;
  file_type: string;
  entity_type?: string;
  entity_id?: string;
  is_public?: boolean;
  workspace_id: string;
  uploaded_by: string;
}

export interface FilePermissions {
  can_download: boolean;
  can_delete: boolean;
  can_update: boolean;
}

export class FilesService {
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private static readonly ALLOWED_MIME_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
  ];

  static async uploadFile(data: UploadFileData) {
    // Validate file size
    if (data.file.size > this.MAX_FILE_SIZE) {
      throw new HTTPException(413, { message: "File size exceeds maximum limit of 50MB" });
    }

    // Validate file type
    if (!this.ALLOWED_MIME_TYPES.includes(data.file.type)) {
      throw new HTTPException(400, { message: `File type ${data.file.type} is not allowed` });
    }

    // Generate unique filename
    const fileExtension = data.file.name.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`;
    
    // Create storage path: {workspace_id}/{entity_type}/{entity_id}/{file_name}
    let storagePath = `${data.workspace_id}`;
    if (data.entity_type && data.entity_id) {
      storagePath += `/${data.entity_type}/${data.entity_id}`;
    }
    storagePath += `/${uniqueFileName}`;

    // Save locally under public/uploads
    const uploadRoot = path.resolve(process.cwd(), "public", "uploads");
    const fullDir = path.dirname(path.join(uploadRoot, storagePath));
    await fs.promises.mkdir(fullDir, { recursive: true });
    const fullPath = path.join(uploadRoot, storagePath);
    const buffer = Buffer.from(await data.file.arrayBuffer());
    await fs.promises.writeFile(fullPath, buffer);

    // Get public URL if file is public
    let storageUrl: string | undefined;
    if (data.is_public) {
      storageUrl = `/uploads/${storagePath}`;
    }

    // Save file metadata to database
    const [fileRecord] = await db.insert(files).values({
      workspaceId: data.workspace_id,
      uploadedBy: data.uploaded_by,
      fileName: uniqueFileName,
      originalName: data.file.name,
      mimeType: data.file.type,
      fileSize: data.file.size,
      storagePath: storagePath,
      storageUrl: storageUrl,
      fileType: data.file_type,
      entityType: data.entity_type,
      entityId: data.entity_id,
      isPublic: data.is_public || false,
      metadata: {},
    }).returning();

    return {
      success: true,
      file: {
        id: fileRecord.id,
        workspace_id: fileRecord.workspaceId,
        uploaded_by: fileRecord.uploadedBy,
        file_name: fileRecord.fileName,
        original_name: fileRecord.originalName,
        mime_type: fileRecord.mimeType,
        file_size: fileRecord.fileSize,
        storage_path: fileRecord.storagePath,
        storage_url: fileRecord.storageUrl,
        file_type: fileRecord.fileType,
        entity_type: fileRecord.entityType,
        entity_id: fileRecord.entityId,
        is_public: fileRecord.isPublic,
        metadata: fileRecord.metadata as Record<string, any>,
        created_at: fileRecord.createdAt!,
        updated_at: fileRecord.updatedAt!,
      },
    };
  }

  static async getFile(fileId: string, workspaceId: string, userId: string) {
    // Get file from database
    const [fileRecord] = await db
      .select()
      .from(files)
      .where(and(
        eq(files.id, fileId),
        eq(files.workspaceId, workspaceId)
      ))
      .limit(1);

    if (!fileRecord) {
      throw new HTTPException(404, { message: "File not found" });
    }

    // For local storage, serve public URL only for public files
    const signedUrl: string | undefined = fileRecord.isPublic
      ? fileRecord.storageUrl || `/uploads/${fileRecord.storagePath}`
      : undefined;

    // Calculate permissions
    const permissions = this.calculatePermissions(fileRecord, userId);

    return {
      id: fileRecord.id,
      workspace_id: fileRecord.workspaceId,
      uploaded_by: fileRecord.uploadedBy,
      file_name: fileRecord.fileName,
      original_name: fileRecord.originalName,
      mime_type: fileRecord.mimeType,
      file_size: fileRecord.fileSize,
      storage_path: fileRecord.storagePath,
      storage_url: fileRecord.storageUrl,
      signed_url: signedUrl,
      file_type: fileRecord.fileType,
      entity_type: fileRecord.entityType,
      entity_id: fileRecord.entityId,
      is_public: fileRecord.isPublic,
      metadata: fileRecord.metadata as Record<string, any>,
      created_at: fileRecord.createdAt!,
      updated_at: fileRecord.updatedAt!,
      can_download: permissions.can_download,
      can_delete: permissions.can_delete,
      can_update: permissions.can_update,
    };
  }

  static async deleteFile(fileId: string, workspaceId: string, userId: string) {
    // Get file from database
    const [fileRecord] = await db
      .select()
      .from(files)
      .where(and(
        eq(files.id, fileId),
        eq(files.workspaceId, workspaceId)
      ))
      .limit(1);

    if (!fileRecord) {
      throw new HTTPException(404, { message: "File not found" });
    }

    // Check permissions
    const permissions = this.calculatePermissions(fileRecord, userId);
    if (!permissions.can_delete) {
      throw new HTTPException(403, { message: "Insufficient permissions to delete this file" });
    }

    // Delete local file if exists
    const diskPath = path.resolve(process.cwd(), "public", "uploads", fileRecord.storagePath);
    try {
      await fs.promises.unlink(diskPath);
    } catch {}

    // Delete from database
    await db
      .delete(files)
      .where(eq(files.id, fileId));

    return {
      success: true,
      message: "File deleted successfully",
      deleted_file: {
        id: fileRecord.id,
        file_name: fileRecord.fileName,
        storage_path: fileRecord.storagePath,
      },
    };
  }

  private static calculatePermissions(fileRecord: any, userId: string): FilePermissions {
    // Basic permission logic - can be extended based on business rules
    const isOwner = fileRecord.uploadedBy === userId;
    
    return {
      can_download: true, // All workspace members can download
      can_delete: isOwner, // Only file owner can delete
      can_update: isOwner, // Only file owner can update
    };
  }
}