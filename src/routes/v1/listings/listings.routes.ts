import { create<PERSON>oute, z } from "@hono/zod-openapi";
import { 
  deleteResponseSchema, 
  paginationSchema 
} from "@/lib/commonApiSchema";

// =============================================================================
// BASE SCHEMAS (mapped from database schema)
// =============================================================================

// Base listing schema matching the database structure
const baseListingSchema = z.object({
  // Core required fields
  businessName: z.string().min(1, "Business name is required"),
  industry: z.string().min(1, "Industry is required"),
  
  // Core optional fields
  assignedTo: z.string().uuid().optional(),
  askingPrice: z.coerce.number().positive().optional(),
  cashFlowSde: z.coerce.number().optional(),
  annualRevenue: z.coerce.number().positive().optional(),
  status: z.string().default('draft'),
  generalLocation: z.string().optional(),
  yearEstablished: z.coerce.number().int().min(1800).max(new Date().getFullYear()).optional(),
  employees: z.coerce.number().int().min(0).optional(),
  ownerHoursWeek: z.coerce.number().int().min(0).max(168).optional(),
  dateListed: z.string().date().optional(),
  
  // Legacy fields for compatibility
  title: z.string().optional(),
  description: z.string().optional(),
  price: z.coerce.number().positive().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  propertyType: z.string().optional(),
  squareFootage: z.coerce.number().int().min(0).optional(),
  lotSize: z.coerce.number().optional(),
  yearBuilt: z.coerce.number().int().min(1800).max(new Date().getFullYear()).optional(),
  bedrooms: z.coerce.number().int().min(0).optional(),
  bathrooms: z.coerce.number().optional(),
  listingType: z.string().optional().default('business_sale'),
  teamVisibility: z.string().optional().default('all'),
  internalNotes: z.array(z.any()).optional(),
  photos: z.array(z.string()).optional(),
  documents: z.array(z.string()).optional(),
  featuredPhoto: z.string().optional(),
  virtualTourUrl: z.string().url().optional(),
  mlsNumber: z.string().optional(),
  listingDate: z.string().date().optional(),
  expirationDate: z.string().date().optional(),
  daysOnMarket: z.coerce.number().int().min(0).optional(),
}).openapi("BaseListing");

// Base listing details schema
const baseListingDetailsSchema = z.object({
  businessDescription: z.string().optional(),
  briefDescription: z.string().optional(),
  financialDetails: z.object({
    revenue2023: z.number().optional(),
    ebitda: z.number().optional(),
    assetsIncluded: z.array(z.string()).optional(),
    inventoryValue: z.number().optional(),
    additionalFinancialInfo: z.record(z.any()).optional(),
  }).optional(),
  operations: z.object({
    businessModel: z.string().optional(),
    keyFeatures: z.array(z.string()).optional(),
    competitiveAdvantages: z.array(z.string()).optional(),
    operationalDetails: z.record(z.any()).optional(),
  }).optional(),
  growthOpportunities: z.array(z.string()).optional(),
  reasonForSale: z.string().optional(),
  trainingPeriod: z.string().optional(),
  supportType: z.string().optional(),
  financingAvailable: z.boolean().optional().default(false),
  equipmentHighlights: z.array(z.string()).optional(),
  supplierRelationships: z.string().optional(),
  realEstateStatus: z.enum(['owned', 'leased', 'included', 'not_included', 'negotiable']).optional(),
  leaseDetails: z.object({
    leaseTerms: z.string().optional(),
    monthlyRent: z.number().optional(),
    leaseExpiration: z.string().date().optional(),
    renewalOptions: z.string().optional(),
    landlordInfo: z.record(z.any()).optional(),
  }).optional(),
}).openapi("BaseListingDetails");

// Base status history schema
const baseListingStatusHistorySchema = z.object({
  listingId: z.string().uuid(),
  workspaceId: z.string().uuid(),
  changedBy: z.string().uuid(),
  fromStatus: z.string().optional(),
  toStatus: z.string(),
  reason: z.string().optional(),
  notes: z.string().optional(),
}).openapi("BaseListingStatusHistory");

// Base listing notes schema
const baseListingNotesSchema = z.object({
  listingId: z.string().uuid(),
  organizationId: z.string(),
  createdBy: z.string(),
  content: z.string().min(1, "Content is required"),
  mentions: z.array(z.string()).optional().default([]),
  isPrivate: z.boolean().optional().default(false),
}).openapi("BaseListingNotes");

// =============================================================================
// ENUMS
// =============================================================================

export const ListingStatusEnum = z.enum([
  'draft', 'active', 'under_contract', 'sold', 'confidential', 'expired', 'withdrawn'
]).openapi("ListingStatus");

export const RealEstateStatusEnum = z.enum([
  'owned', 'leased', 'included', 'not_included', 'negotiable'
]).openapi("RealEstateStatus");

// =============================================================================
// REQUEST SCHEMAS (extending/omitting from base)
// =============================================================================

// Create request schema - omit system fields, add details
export const createListingRequestSchema = baseListingSchema
  .omit({
    // Remove fields that are auto-generated or system-managed
    daysOnMarket: true,
  })
  .extend({
    // Add nested details for comprehensive listing creation
    details: baseListingDetailsSchema.optional(),
  })
  .openapi("CreateListingRequest");

// Save as draft request schema - all fields optional except workspace context
export const saveDraftListingRequestSchema = baseListingSchema
  .omit({
    // Remove fields that are auto-generated or system-managed
    daysOnMarket: true,
    businessName: true, // Make business name optional for drafts
    industry: true, // Make industry optional for drafts
  })
  .extend({
    // Make core fields optional for drafts
    businessName: z.string().optional(),
    industry: z.string().optional(),
    // Add nested details for comprehensive listing creation
    details: baseListingDetailsSchema.optional(),
  })
  .openapi("SaveDraftListingRequest");

// Update request schema - make everything optional and add status change tracking
export const updateListingRequestSchema = createListingRequestSchema
  .partial()
  .extend({
    // Additional fields for status change tracking when status is updated
    reason: z.string().optional().describe("Reason for status change (used when updating status)"),
    notes: z.string().optional().describe("Additional notes for status change (used when updating status)"),
  })
  .openapi("UpdateListingRequest");

// Listing notes request schemas
export const createListingNoteRequestSchema = z.object({
  content: z.string().min(1, "Content is required"),
  mentions: z.array(z.string()).optional().default([]),
  isPrivate: z.boolean().optional().default(false),
}).openapi("CreateListingNoteRequest");

export const updateListingNoteRequestSchema = z.object({
  content: z.string().min(1).optional(),
  mentions: z.array(z.string()).optional(),
  isPrivate: z.boolean().optional(),
}).openapi("UpdateListingNoteRequest");

// Bulk create request schema
export const bulkCreateListingRequestSchema = z.object({
  listings: z.array(createListingRequestSchema).min(1).max(100),
}).openapi("BulkCreateListingRequest");

// Status update request schema
export const updateListingStatusRequestSchema = z.object({
  status: ListingStatusEnum,
  reason: z.string().optional(),
  notes: z.string().optional(),
}).openapi("UpdateListingStatusRequest");

// CSV import schema (all string inputs for CSV parsing)
export const csvBulkImportRequestSchema = z.object({
  businessName: z.string().min(1, "Business name is required").describe("**Required** - Name of the business"),
  industry: z.string().min(1, "Industry is required").describe("**Required** - Industry category"),
  askingPrice: z.string().optional().describe("Asking price for the business"),
  cashFlowSde: z.string().optional().describe("Seller's Discretionary Earnings (SDE)"),
  annualRevenue: z.string().optional().describe("Annual revenue of the business"),
  status: z.string().optional().describe("Listing status (Active, Under Contract, Sold, Confidential, etc.)"),
  generalLocation: z.string().optional().describe("General location (city/state/region)"),
  yearEstablished: z.string().optional().describe("Year the business was established"),
  employees: z.string().optional().describe("Number of employees"),
  ownerHoursWeek: z.string().optional().describe("Owner hours per week"),
  dateListed: z.string().optional().describe("Date when listing was created (YYYY-MM-DD format)"),
  daysListed: z.string().optional().describe("Number of days the listing has been active"),
  businessDescription: z.string().optional().describe("Detailed business description"),
  briefDescription: z.string().optional().describe("Brief business summary"),
  financialDetails: z.string().optional().describe("JSON string containing 2023_revenue, ebitda, assets_included, inventory_value"),
  operations: z.string().optional().describe("JSON string containing business_model, key_features, competitive_advantages"),
  growthOpportunities: z.string().optional().describe("Comma-separated list of growth opportunities"),
  reasonForSale: z.string().optional().describe("Reason for selling the business"),
  trainingPeriod: z.string().optional().describe("Training period offered to buyer"),
  supportType: z.string().optional().describe("Type of support provided to buyer"),
  financingAvailable: z.string().optional().describe("Whether financing is available (true/false)"),
  equipmentHighlights: z.string().optional().describe("Comma-separated list of equipment highlights"),
  supplierRelationships: z.string().optional().describe("Information about supplier relationships"),
  realEstateStatus: z.string().optional().describe("Real estate status (owned, leased, included, not_included, negotiable)"),
  leaseDetails: z.string().optional().describe("JSON string containing lease_terms, monthly_rent, lease_expiration, renewal_options"),
}).openapi("CsvBulkImportRequest");

// =============================================================================
// RESPONSE SCHEMAS (extending base with computed fields)
// =============================================================================

// Main listing response schema - add computed fields
export const listingResponseSchema = baseListingSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    workspaceId: z.string().uuid(),
    createdBy: z.string().uuid(),
    daysListed: z.number().int().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
    
    // Computed/joined fields
    details: baseListingDetailsSchema.optional(),
    createdByName: z.string().optional(),
    assignedToName: z.string().optional(),
  })
  .openapi("ListingResponse");

// Status history response schema
export const selectListingStatusHistorySchema = baseListingStatusHistorySchema
  .extend({
    id: z.string().uuid(),
    createdAt: z.string(),
    changedByName: z.string().optional(),
  })
  .openapi("ListingStatusHistory");

// Listing notes response schema
export const listingNotesResponseSchema = baseListingNotesSchema
  .extend({
    id: z.string().uuid(),
    createdAt: z.string(),
    updatedAt: z.string(),
    createdByName: z.string().optional(),
  })
  .openapi("ListingNotesResponse");

// =============================================================================
// REUSABLE RESPONSE WRAPPERS
// =============================================================================

// Single listing response wrapper
export const singleListingResponseSchema = z.object({
  success: z.boolean(),
  data: listingResponseSchema,
}).openapi("SingleListingResponse");

// Listing list response wrapper
export const listingListResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(listingResponseSchema),
  pagination: paginationSchema,
}).openapi("ListingListResponse");

// Bulk create response wrapper
export const bulkCreateResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    created: z.array(listingResponseSchema),
    failed: z.array(z.object({
      index: z.number(),
      error: z.string(),
      data: z.record(z.any()),
    })),
  }),
}).openapi("BulkCreateResponse");

// Status change response wrapper
export const statusChangeResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    listing: listingResponseSchema,
    statusChange: selectListingStatusHistorySchema,
  }),
}).openapi("StatusChangeResponse");

// CSV validation response wrapper
export const csvValidationResponseSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  data: z.object({
    preview: z.array(z.object({
      row: z.number(),
      businessName: z.string(),
      industry: z.string(),
      askingPrice: z.number().optional(),
      status: z.string(),
      location: z.string().optional(),
      validationStatus: z.enum(['valid', 'invalid']),
      errors: z.array(z.string()),
    })),
    valid: z.array(z.object({
      row: z.number(),
      data: z.record(z.any()),
      originalCsvData: z.record(z.any()),
    })),
    invalid: z.array(z.object({
      row: z.number(),
      errors: z.array(z.string()),
      data: z.record(z.any()),
      originalCsvData: z.record(z.any()),
    })),
    summary: z.object({
      totalRows: z.number(),
      validRows: z.number(),
      invalidRows: z.number(),
      requiredFields: z.array(z.string()),
      optionalFields: z.array(z.string()),
    }),
  }),
}).openapi("CsvValidationResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// Routes
export const listListingsRoute = createRoute({
  method: "get",
  path: "/v1/listings",
  request: {
    query: z.object({
      page: z.string().regex(/^\d+$/).transform(Number).optional().default("1"),
      limit: z.string().regex(/^\d+$/).transform(Number).optional().default("20"),
      status: z.string().optional(),
      industry: z.string().optional(),
      assignedTo: z.string().uuid().optional(),
      minPrice: z.string().regex(/^\d+(\.\d+)?$/).transform(Number).optional(),
      maxPrice: z.string().regex(/^\d+(\.\d+)?$/).transform(Number).optional(),
      location: z.string().optional(),
      sortBy: z.enum(['created_at', 'updated_at', 'asking_price', 'business_name', 'date_listed', 'days_listed']).optional().default('created_at'),
      sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
      search: z.string().optional(),
    }).openapi("ListingsQuery"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: listingListResponseSchema,
        },
      },
      description: "List of listings retrieved successfully",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

export const getListingRoute = createRoute({
  method: "get",
  path: "/v1/listings/{listingId}",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("ListingParams"),
    query: z.object({
      includeDetails: z.enum(['true', 'false']).optional().default('true'),
    }).openapi("GetListingQuery"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Listing retrieved successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const createListingRoute = createRoute({
  method: "post",
  path: "/v1/listings",
  request: {
    body: {
      content: {
        "application/json": {
          schema: createListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Listing created successfully",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

export const saveDraftListingRoute = createRoute({
  method: "post",
  path: "/v1/listings/draft",
  request: {
    body: {
      content: {
        "application/json": {
          schema: saveDraftListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Draft listing saved successfully",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
  summary: "Save Listing as Draft",
  description: "Save a listing with incomplete data as a draft. All fields are optional and data is stored in the _draft column.",
});

export const updateDraftListingRoute = createRoute({
  method: "put",
  path: "/v1/listings/{listingId}/draft",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("UpdateDraftListingParams"),
    body: {
      content: {
        "application/json": {
          schema: saveDraftListingRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Draft listing updated successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors or listing is not a draft",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
  summary: "Update Draft Listing",
  description: "Update an existing draft listing. Only works for listings with status 'draft'.",
});

export const updateListingRoute = createRoute({
  method: "put",
  path: "/v1/listings/{listingId}",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("UpdateListingParams"),
    body: {
      content: {
        "application/json": {
          schema: updateListingRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: singleListingResponseSchema,
        },
      },
      description: "Listing updated successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const deleteListingRoute = createRoute({
  method: "delete",
  path: "/v1/listings/{listingId}",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("DeleteListingParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteResponseSchema,
        },
      },
      description: "Listing deleted successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const bulkCreateListingsRoute = createRoute({
  method: "post",
  path: "/v1/listings/bulk",
  request: {
    body: {
      content: {
        "application/json": {
          schema: bulkCreateListingRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: bulkCreateResponseSchema,
        },
      },
      description: "Bulk listing creation completed",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
  },
  tags: ["Listings"],
});

export const bulkCreateListingsCsvRoute = createRoute({
  method: "post",
  path: "/v1/listings/bulk/csv",
  request: {
    body: {
      content: {
        "multipart/form-data": {
          schema: z.object({
            file: z.any().describe("CSV file containing listing data for import"),
          }).openapi("CSVUploadRequest"),
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: bulkCreateResponseSchema,
        },
      },
      description: "CSV bulk import completed successfully - valid records saved to database",
    },
    400: {
      description: "Bad request - validation errors or invalid CSV format",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    413: {
      description: "Payload too large - file size exceeds limit",
    },
  },
  tags: ["Listings"],
  summary: "Import CSV Listings Data",
  description: "Parse and import CSV file containing business listings data. The CSV file is processed entirely in memory (not saved to disk), validated, and valid records are saved to the database. Returns detailed results including successful and failed imports.",
});

export const updateListingStatusRoute = createRoute({
  method: "patch",
  path: "/v1/listings/{listingId}/status",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("UpdateStatusParams"),
    body: {
      content: {
        "application/json": {
          schema: updateListingStatusRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: statusChangeResponseSchema,
        },
      },
      description: "Listing status updated successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

export const getListingStatusHistoryRoute = createRoute({
  method: "get",
  path: "/v1/listings/{listingId}/status-history",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("StatusHistoryParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.array(selectListingStatusHistorySchema),
          }).openapi("StatusHistoryResponse"),
        },
      },
      description: "Listing status history retrieved successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings"],
});

// Listing Notes routes
export const getListingNotesRoute = createRoute({
  method: "get",
  path: "/v1/listings/{listingId}/notes",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("ListingNotesParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.array(listingNotesResponseSchema),
          }).openapi("ListingNotesListResponse"),
        },
      },
      description: "Listing notes retrieved successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings", "Notes"],
});

export const createListingNoteRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listingId}/notes",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("CreateListingNoteParams"),
    body: {
      content: {
        "application/json": {
          schema: createListingNoteRequestSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: listingNotesResponseSchema,
          }).openapi("CreateListingNoteResponse"),
        },
      },
      description: "Note created successfully",
    },
    404: {
      description: "Listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
  },
  tags: ["Listings", "Notes"],
});

export const updateListingNoteRoute = createRoute({
  method: "put",
  path: "/v1/listings/{listingId}/notes/{noteId}",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
      noteId: z.string().uuid(),
    }).openapi("UpdateListingNoteParams"),
    body: {
      content: {
        "application/json": {
          schema: updateListingNoteRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: listingNotesResponseSchema,
          }).openapi("UpdateListingNoteResponse"),
        },
      },
      description: "Note updated successfully",
    },
    404: {
      description: "Note or listing not found",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - can only update your own notes",
    },
  },
  tags: ["Listings", "Notes"],
});

export const deleteListingNoteRoute = createRoute({
  method: "delete",
  path: "/v1/listings/{listingId}/notes/{noteId}",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
      noteId: z.string().uuid(),
    }).openapi("DeleteListingNoteParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: deleteResponseSchema,
        },
      },
      description: "Note deleted successfully",
    },
    404: {
      description: "Note or listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - can only delete your own notes",
    },
  },
  tags: ["Listings", "Notes"],
});

// AI-powered routes
export const generateListingDescriptionRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listingId}/ai/generate-description",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("GenerateDescriptionParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              description: z.string(),
            }),
          }).openapi("GenerateDescriptionResponse"),
        },
      },
      description: "AI-generated listing description created successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const analyzeListingDataRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listingId}/ai/analyze",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("AnalyzeListingParams"),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              analysis: z.string(),
            }),
          }).openapi("AnalyzeListingResponse"),
        },
      },
      description: "AI analysis of listing data completed successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const generateListingInsightsRoute = createRoute({
  method: "post",
  path: "/v1/listings/{listingId}/ai/insights",
  request: {
    params: z.object({
      listingId: z.string().uuid(),
    }).openapi("InsightsParams"),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            customPrompt: z.string().optional().describe("Custom system prompt for AI analysis"),
          }).openapi("InsightsRequest"),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              insights: z.string(),
            }),
          }).openapi("InsightsResponse"),
        },
      },
      description: "AI insights generated successfully",
    },
    404: {
      description: "Listing not found",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    403: {
      description: "Forbidden - insufficient permissions",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

export const customAICompletionRoute = createRoute({
  method: "post",
  path: "/v1/listings/ai/chat",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            systemPrompt: z.string().min(1, "System prompt is required"),
            userInput: z.string().min(1, "User input is required"),
            model: z.string().optional().default("gpt-3.5-turbo"),
            maxTokens: z.number().int().min(1).max(4000).optional().default(1000),
            temperature: z.number().min(0).max(2).optional().default(0.7),
          }).openapi("AICompletionRequest"),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              content: z.string(),
              model: z.string(),
              usage: z.object({
                promptTokens: z.number(),
                completionTokens: z.number(),
                totalTokens: z.number(),
              }).optional(),
            }),
          }).openapi("AICompletionResponse"),
        },
      },
      description: "AI chat completion successful",
    },
    400: {
      description: "Bad request - validation errors",
    },
    401: {
      description: "Unauthorized - authentication required",
    },
    500: {
      description: "Internal server error - AI service unavailable",
    },
  },
  tags: ["Listings", "AI"],
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./listings.controller";

export const listingsRouter = createRouter()
  .openapi(listListingsRoute, handlers.listListings)
  .openapi(getListingRoute, handlers.getListing)
  .openapi(createListingRoute, handlers.createListing)
  .openapi(saveDraftListingRoute, handlers.saveDraftListing)
  .openapi(updateDraftListingRoute, handlers.updateDraftListing)
  .openapi(updateListingRoute, handlers.updateListing)
  .openapi(deleteListingRoute, handlers.deleteListing)
  .openapi(bulkCreateListingsCsvRoute, handlers.bulkCreateListingsCsv)
  .openapi(getListingStatusHistoryRoute, handlers.getListingStatusHistory)
  .openapi(getListingNotesRoute, handlers.getListingNotes)
  .openapi(createListingNoteRoute, handlers.createListingNote)
  .openapi(updateListingNoteRoute, handlers.updateListingNote)
  .openapi(deleteListingNoteRoute, handlers.deleteListingNote);

// Export router as default for backward compatibility during migration
export default listingsRouter;