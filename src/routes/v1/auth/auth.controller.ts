import { HTTPException } from "hono/http-exception";
import { AuthService } from "./auth.service";

/**
 * Sign up with email and password
 * Controller orchestrates the complete user onboarding process by wiring together service functions:
 * 1. Create user account with Better Auth (session cookie automatically set)
 * 2. Create organization (with automatic membership via Better Auth)
 * 3. Create user profile
 * Note: Authentication is handled via session cookies, no manual token management needed
 */
export const signUpEmail = async (c: any) => {
  try {
    const body = c.req.valid("json");

    // Step 1: Create user account with Better Auth
    const { authData, betterAuthResponse } = await AuthService.createUserAccount({
      name: body.name,
      email: body.email,
      password: body.password,
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
      image: body.image,
      callbackURL: body.callbackURL,
      rememberMe: body.rememberMe,
    });

    // Step 2: Create organization
    const organizationId = await AuthService.createOrganization(authData.user.id, {
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
    });

    // Step 3: Create user profile (membership is automatically created by Better Auth)
    await AuthService.createUserProfile(authData.user.id, organizationId, {
      companyName: body.company_name,
      companyType: body.company_type || "individual",
      firstName: body.first_name,
      lastName: body.last_name,
      phone: body.phone,
      licenseNumber: body.license_number,
      website: body.website,
      businessAddress: body.business_address,
      marketingConsent: body.marketing_consent || false,
      termsAccepted: body.terms_accepted,
    });

    // Step 4: Get enriched data for response
    const enrichedData = await AuthService.getEnrichedAuthData(authData.user.id);

    // Step 5: Build final response
    const result = {
      user: authData.user,
      session: authData.session,
      organization: enrichedData.organization,
      profile: enrichedData.profile,
    };

    // Step 6: Forward Better Auth session cookies to client
    const setCookieHeaders = betterAuthResponse.headers.getSetCookie?.() ||
                            betterAuthResponse.headers.get('set-cookie')?.split(', ') || [];

    for (const cookieHeader of setCookieHeaders) {
      c.header('Set-Cookie', cookieHeader);
    }

    return c.json(result, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Sign up controller error:", error);
    throw new HTTPException(500, { message: "Failed to create user account" });
  }
};

/**
 * Sign in with email and password
 * Authentication is handled via session cookies set by Better Auth
 */
export const signInEmail = async (c: any) => {
  try {
    const body = c.req.valid("json");

    // Validate required fields
    if (!body.email || !body.password) {
      throw new HTTPException(400, {
        message: "Email and password are required"
      });
    }

    // Call auth service
    const { authResponse, betterAuthResponse } = await AuthService.signInEmail({
      email: body.email,
      password: body.password,
      callbackURL: body.callbackURL,
      rememberMe: body.rememberMe,
    });

    // Forward Better Auth session cookies to client
    const setCookieHeaders = betterAuthResponse.headers.getSetCookie?.() ||
                            betterAuthResponse.headers.get('set-cookie')?.split(', ') || [];

    for (const cookieHeader of setCookieHeaders) {
      c.header('Set-Cookie', cookieHeader);
    }

    return c.json(authResponse, 200);
  } catch (error: any) {
    if (error instanceof HTTPException) {
      throw error;
    }

    console.error("Sign in controller error:", error);
    throw new HTTPException(500, { message: "Failed to authenticate user" });
  }
};
