import { describe, it, expect, beforeEach } from "vitest";
import { createTestApp } from "@/lib/create-app";
import authModule from "./auth.routes";

describe("Auth API - Cookie Integration Tests", () => {
  const app = createTestApp(authModule as any);

  describe("POST /v1/auth/sign-up/email", () => {
    it("should set session cookies when creating a new user", async () => {
      const signUpData = {
        first_name: "Test",
        last_name: "User",
        email: `test-${Date.now()}@example.com`,
        company_name: "Test Company",
        company_type: "individual",
        password: "TestPassword123!",
        confirm_password: "TestPassword123!",
        terms_accepted: true,
        phone: "+**********",
        license_number: "TEST123",
        website: "https://test.com",
        business_address: "123 Test St",
        marketing_consent: false,
        rememberMe: true
      };

      const res = await app.request("/v1/auth/sign-up/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(signUpData),
      });

      // Should succeed
      expect(res.status).toBe(200);

      // Should have session cookies set
      const setCookieHeaders = res.headers.getSetCookie?.() || [];
      expect(setCookieHeaders.length).toBeGreaterThan(0);

      // Should have Better Auth session cookies
      const hasSessionCookie = setCookieHeaders.some(cookie => 
        cookie.includes('better-auth.session_token') || 
        cookie.includes('session') ||
        cookie.includes('auth')
      );
      expect(hasSessionCookie).toBe(true);

      // Response should contain user data
      const data = await res.json();
      expect(data.user).toBeDefined();
      expect(data.user.email).toBe(signUpData.email);
      expect(data.session).toBeDefined();
      expect(data.organization).toBeDefined();
      expect(data.profile).toBeDefined();
    });
  });

  describe("POST /v1/auth/sign-in/email", () => {
    it("should set session cookies when signing in existing user", async () => {
      // First create a user
      const signUpData = {
        first_name: "Test",
        last_name: "User",
        email: `signin-test-${Date.now()}@example.com`,
        company_name: "Test Company",
        company_type: "individual",
        password: "TestPassword123!",
        confirm_password: "TestPassword123!",
        terms_accepted: true,
        rememberMe: true
      };

      const signUpRes = await app.request("/v1/auth/sign-up/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(signUpData),
      });

      expect(signUpRes.status).toBe(200);

      // Now sign in with the same user
      const signInData = {
        email: signUpData.email,
        password: signUpData.password,
        rememberMe: true
      };

      const res = await app.request("/v1/auth/sign-in/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(signInData),
      });

      // Should succeed
      expect(res.status).toBe(200);

      // Should have session cookies set
      const setCookieHeaders = res.headers.getSetCookie?.() || [];
      expect(setCookieHeaders.length).toBeGreaterThan(0);

      // Should have Better Auth session cookies
      const hasSessionCookie = setCookieHeaders.some(cookie => 
        cookie.includes('better-auth.session_token') || 
        cookie.includes('session') ||
        cookie.includes('auth')
      );
      expect(hasSessionCookie).toBe(true);

      // Response should contain user data
      const data = await res.json();
      expect(data.user).toBeDefined();
      expect(data.user.email).toBe(signInData.email);
      expect(data.session).toBeDefined();
      expect(data.organization).toBeDefined();
      expect(data.profile).toBeDefined();
    });

    it("should return 401 for invalid credentials", async () => {
      const signInData = {
        email: "<EMAIL>",
        password: "WrongPassword123!",
        rememberMe: false
      };

      const res = await app.request("/v1/auth/sign-in/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(signInData),
      });

      expect(res.status).toBe(401);
    });
  });

  describe("Cookie Security", () => {
    it("should set httpOnly and secure cookies in production", async () => {
      // This test would need to mock NODE_ENV=production
      // For now, we'll just verify that cookies are being set
      const signUpData = {
        first_name: "Test",
        last_name: "User", 
        email: `security-test-${Date.now()}@example.com`,
        company_name: "Test Company",
        company_type: "individual",
        password: "TestPassword123!",
        confirm_password: "TestPassword123!",
        terms_accepted: true,
        rememberMe: true
      };

      const res = await app.request("/v1/auth/sign-up/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(signUpData),
      });

      expect(res.status).toBe(200);

      const setCookieHeaders = res.headers.getSetCookie?.() || [];
      expect(setCookieHeaders.length).toBeGreaterThan(0);

      // Check that cookies have security attributes
      const sessionCookie = setCookieHeaders.find(cookie => 
        cookie.includes('better-auth.session_token') || 
        cookie.includes('session') ||
        cookie.includes('auth')
      );

      if (sessionCookie) {
        // In development, secure might not be set, but httpOnly should be
        expect(sessionCookie).toMatch(/httponly/i);
      }
    });
  });
});
