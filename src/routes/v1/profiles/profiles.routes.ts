import { create<PERSON>out<PERSON>, z } from "@hono/zod-openapi";
import { authMiddleware } from "@/middlewares/auth";

// =============================================================================
// BASE PROFILE SCHEMAS
// =============================================================================

// Base profile schema mapped from database structure
const baseProfileSchema = z
  .object({
    workspaceId: z.string().uuid().optional(),
    userId: z.string().uuid().optional(),
    email: z.string().email("Invalid email format"),
    firstName: z.string().min(1).optional(),
    lastName: z.string().min(1).optional(),
    displayName: z.string().optional(),
    bio: z.string().optional(),
    role: z.enum(["owner", "admin", "member", "viewer"]).optional(),
    phone: z
      .string()
      .regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format")
      .optional(),
    licenseNumber: z.string().optional(),
    avatarUrl: z.string().optional(),
    specialties: z.array(z.string()).optional(),
    isActive: z.boolean().default(true),
    preferences: z.record(z.any()).optional(),
  })
  .openapi("BaseProfile");

// Base preferences schema for reuse
const basePreferencesSchema = z
  .object({
    notifications: z
      .object({
        emailNotifications: z.boolean(),
        pushNotifications: z.boolean(),
        listingUpdates: z.boolean(),
        teamUpdates: z.boolean(),
        systemUpdates: z.boolean(),
      })
      .optional(),
    display: z
      .object({
        timezone: z.string(),
        dateFormat: z.string(),
        currency: z.string(),
        language: z.string(),
      })
      .optional(),
    privacy: z
      .object({
        profileVisibility: z.enum(["team", "public", "private"]),
        contactVisibility: z.enum(["team", "public", "private"]),
      })
      .optional(),
  })
  .openapi("ProfilePreferences");

// =============================================================================
// REQUEST SCHEMAS (extending/reducing from base)
// =============================================================================

// Profile update request schema - only editable fields
export const UpdateProfileRequestSchema = z
  .object({
    firstName: z.string().min(1).optional(),
    lastName: z.string().min(1).optional(),
    displayName: z.string().optional(),
    bio: z.string().optional(),
    phone: z
      .string()
      .regex(/^\+?[\d\s\-\(\)]+$/, "Invalid phone format")
      .optional(),
    licenseNumber: z.string().optional(),
    avatarFileId: z.string().uuid().optional(),
    specialties: z.array(z.string()).optional(),
    preferences: basePreferencesSchema.optional(),
  })
  .openapi("UpdateProfileRequest");

// =============================================================================
// RESPONSE SCHEMAS (extending base with computed fields)
// =============================================================================

// Full profile response schema
export const ProfileResponseSchema = baseProfileSchema
  .extend({
    // System-generated fields
    id: z.string().uuid(),
    invitedAt: z.string().optional(),
    joinedAt: z.string().optional(),
    invitedBy: z.string().uuid().optional(),
    lastLoginAt: z.string().optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
  })
  .openapi("ProfileResponse");

// Extended update response with operation details
export const UpdateProfileResponseSchema = z
  .object({
    id: z.string(),
    workspaceId: z.string(),
    email: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    role: z.string(),
    phone: z.string().optional(),
    licenseNumber: z.string().optional(),
    bio: z.string().optional(),
    avatarUrl: z.string().optional(),
    specialties: z.array(z.string()),
    isActive: z.boolean(),
    preferences: z.any(),
    updatedAt: z.string(),

    // Response metadata for operations
    operationsCompleted: z.object({
      profileUpdated: z.boolean(),
      avatarUpdated: z.boolean().optional(),
    }),

    // Warnings or next steps
    warnings: z.array(z.string()).optional(),
  })
  .openapi("UpdateProfileResponse");

// =============================================================================
// ROUTE DEFINITIONS
// =============================================================================

// GET /v1/profiles/me
export const getProfileRoute = createRoute({
  tags: ["Profiles"],
  method: "get",
  path: "/v1/profiles/me",
  summary: "Get current user profile",
  description: "Get current user profile with workspace context",
  middleware: [authMiddleware],
  responses: {
    200: {
      content: {
        "application/json": {
          schema: ProfileResponseSchema,
        },
      },
      description: "Profile retrieved successfully",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "Profile not found",
    },
  },
});

// PUT /v1/profiles/me
export const updateProfileRoute = createRoute({
  tags: ["Profiles"],
  method: "put",
  path: "/v1/profiles/me",
  summary: "Update current user profile",
  description:
    "Update current user profile information (centralized endpoint for all profile updates)",
  middleware: [authMiddleware],
  request: {
    body: {
      content: {
        "application/json": {
          schema: UpdateProfileRequestSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: UpdateProfileResponseSchema,
        },
      },
      description: "Profile updated successfully",
    },
    400: {
      description: "Invalid request data",
    },
    401: {
      description: "Authentication required",
    },
    404: {
      description: "Profile not found",
    },
  },
});

// =============================================================================
// ROUTER ASSEMBLY
// =============================================================================

import { createRouter } from "@/lib/create-app";
import * as handlers from "./profiles.controller";

export const profilesRouter = createRouter()
  .openapi(getProfileRoute, handlers.getProfile)
  .openapi(updateProfileRoute, handlers.updateProfile);

export default profilesRouter;


