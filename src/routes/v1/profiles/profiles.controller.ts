import type { Context } from "hono";
import { getAuthenticatedUser, getUserWorkspace } from "@/lib/auth-utils";
import { ProfilesService } from "./profiles.service";
import { UpdateProfileRequestSchema } from "./profiles.routes";
import type { UpdateProfileData } from "./profiles.service";

export async function getProfile(c: Context) {
  const user = getAuthenticatedUser(c);
  const workspace = getUserWorkspace(c);

  const profile = await ProfilesService.getUserProfile(user.id, workspace.id);

  return c.json(profile, 200);
}

export async function updateProfile(c: Context) {
  const user = getAuthenticatedUser(c);
  const workspace = getUserWorkspace(c);

  const body = await c.req.json();
  const result = UpdateProfileRequestSchema.safeParse(body);

  if (!result.success) {
    return c.json({ error: "Validation failed", details: result.error.errors }, 400);
  }

  const updateDataInput = result.data;

  // Map camelCase preferences to snake_case expected by service
  const mappedUpdateData: UpdateProfileData = {
    firstName: updateDataInput.firstName,
    lastName: updateDataInput.lastName,
    displayName: updateDataInput.displayName,
    bio: updateDataInput.bio,
    phone: updateDataInput.phone,
    licenseNumber: updateDataInput.licenseNumber,
    specialties: updateDataInput.specialties,
    avatarFileId: updateDataInput.avatarFileId,
    preferences: updateDataInput.preferences
      ? {
          notifications: updateDataInput.preferences.notifications
            ? {
                email_notifications:
                  updateDataInput.preferences.notifications.emailNotifications,
                push_notifications:
                  updateDataInput.preferences.notifications.pushNotifications,
                listing_updates:
                  updateDataInput.preferences.notifications.listingUpdates,
                team_updates:
                  updateDataInput.preferences.notifications.teamUpdates,
                system_updates:
                  updateDataInput.preferences.notifications.systemUpdates,
              }
            : undefined,
          display: updateDataInput.preferences.display
            ? {
                timezone: updateDataInput.preferences.display.timezone,
                date_format: updateDataInput.preferences.display.dateFormat,
                currency: updateDataInput.preferences.display.currency,
                language: updateDataInput.preferences.display.language,
              }
            : undefined,
          privacy: updateDataInput.preferences.privacy
            ? {
                profile_visibility:
                  updateDataInput.preferences.privacy.profileVisibility,
                contact_visibility:
                  updateDataInput.preferences.privacy.contactVisibility,
              }
            : undefined,
        }
      : undefined,
  };

  const updatedProfile = await ProfilesService.updateUserProfile(
    user.id,
    workspace.id,
    mappedUpdateData
  );

  return c.json(updatedProfile, 200);
}

