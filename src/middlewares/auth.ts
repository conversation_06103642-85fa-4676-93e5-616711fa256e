import type { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";
import db from "@/db";
import { userProfiles, user as usersTable, organization } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { auth as betterAuth } from "@/lib/auth";
interface TokenUser {
  id: string;
  email?: string;
}

export interface AuthenticatedUser extends TokenUser {
  profile?: {
    id: string;
    workspaceId: string;
    role: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    email: string;
  };
  workspace?: {
    id: string;
    companyName: string;
    subscriptionPlan: string;
    status: string;
  };
}

/**
 * Authentication middleware that verifies Better Auth sessions via cookies
 */
export async function authMiddleware(c: Context, next: Next) {
  // Skip authentication for OPTIONS requests (CORS preflight)
  if (c.req.method === 'OPTIONS') {
    await next();
    return;
  }

  try {
    // Debug: Log cookies for troubleshooting
    const cookieHeader = c.req.header('cookie');
    if (process.env.NODE_ENV === 'development') {
      console.log('🍪 Auth middleware - cookies:', cookieHeader);
    }

    // Validate Better Auth session from cookies
    const session = await betterAuth.api.getSession({
      headers: c.req.raw.headers
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Auth middleware - session result:', {
        hasSession: !!session?.session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
      });
    }

    if (session?.user) {
      // Load user profile and workspace context
      const authenticatedUser = await loadUserContext({ id: session.user.id, email: session.user.email });

      // Add authenticated user with context to request context
      c.set("user", authenticatedUser);
      await next();
      return;
    }
  } catch (error) {
    console.log('Session validation failed:', error);
  }

  // No valid session found
  throw new HTTPException(401, {
    message: "Authentication required. Please sign in to access this resource."
  });
}

/**
 * Optional authentication middleware - doesn't throw if no session found
 */
export async function optionalAuthMiddleware(c: Context, next: Next) {
  // Skip authentication for OPTIONS requests (CORS preflight)
  if (c.req.method === 'OPTIONS') {
    await next();
    return;
  }

  try {
    // Debug: Log cookies for troubleshooting (optional auth)
    const cookieHeader = c.req.header('cookie');
    if (process.env.NODE_ENV === 'development') {
      console.log('🍪 Optional auth middleware - cookies:', cookieHeader);
    }

    // Try Better Auth session validation from cookies
    const session = await betterAuth.api.getSession({
      headers: c.req.raw.headers
    });

    if (process.env.NODE_ENV === 'development') {
      console.log('🔐 Optional auth middleware - session result:', {
        hasSession: !!session?.session,
        hasUser: !!session?.user,
        userId: session?.user?.id,
      });
    }

    if (session?.user) {
      // Load user profile and workspace context
      const authenticatedUser = await loadUserContext({ id: session.user.id, email: session.user.email });
      c.set("user", authenticatedUser);
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.log('Optional auth failed:', error);
    }
    // Silently fail for optional auth
  }

  await next();
}

/**
 * Load user profile and workspace context
 */
async function loadUserContext(user: TokenUser): Promise<AuthenticatedUser> {
  try {
    console.log('Debug - Loading context for user:', user);

    // Find user profile with workspace information
    const userProfile = await db
      .select({
        id: userProfiles.id,
        workspaceId: userProfiles.organizationId,
        role: usersTable.role,
        firstName: userProfiles.firstName,
        lastName: userProfiles.lastName,
        displayName: userProfiles.displayName,
        email: usersTable.email,
        isActive: userProfiles.isActive,
      })
      .from(userProfiles)
      .leftJoin(usersTable, eq(userProfiles.userId, usersTable.id))
      .where(and(
        eq(userProfiles.userId, user.id),
        eq(userProfiles.isActive, true)
      ))
      .limit(1);

    const profile = userProfile[0];
    console.log('Debug - User profile loaded:', profile);

    // Load workspace/organization details if user has one
    let workspace = undefined;
    if (profile?.workspaceId) {
      console.log('Debug - Loading workspace for ID:', profile.workspaceId);
      const orgResult = await db
        .select({
          id: organization.id,
          companyName: organization.name,
          subscriptionPlan: organization.metadata, // Assuming metadata contains subscription info
          status: organization.metadata, // Assuming metadata contains status info
        })
        .from(organization)
        .where(eq(organization.id, profile.workspaceId))
        .limit(1);

      console.log('Debug - Organization query result:', orgResult);

      if (orgResult[0]) {
        workspace = {
          id: orgResult[0].id,
          companyName: orgResult[0].companyName,
          subscriptionPlan: 'trial', // Default to trial for now
          status: 'active', // Default to active for now
        };
      }
    } else {
      console.log('Debug - No workspaceId found in profile');
    }

    const authenticatedUser: AuthenticatedUser = {
      ...user,
      profile: profile ? {
        id: profile.id,
        workspaceId: profile.workspaceId!,
        role: profile.role!,
        firstName: profile.firstName || undefined,
        lastName: profile.lastName || undefined,
        displayName: profile.displayName || undefined,
        email: profile.email!,
      } : undefined,
      workspace,
    };

    return authenticatedUser;
  } catch (error) {
    // If we can't load context, return user without context
    return user as AuthenticatedUser;
  }
}
/**

 * Role-based authorization middleware
 */
export function requireRole(...allowedRoles: string[]) {
  return async (c: Context, next: Next) => {
    const user = c.get("user") as AuthenticatedUser;
    
    if (!user) {
      throw new HTTPException(401, { message: "Authentication required" });
    }

    if (!user.profile) {
      throw new HTTPException(403, { message: "User profile not found" });
    }

    if (!allowedRoles.includes(user.profile.role)) {
      throw new HTTPException(403, { 
        message: `Access denied. Required roles: ${allowedRoles.join(", ")}` 
      });
    }

    await next();
  };
}

/**
 * Workspace access middleware - ensures user belongs to a workspace
 */
export async function requireWorkspace(c: Context, next: Next) {
  const user = c.get("user") as AuthenticatedUser;
  
  if (!user) {
    throw new HTTPException(401, { message: "Authentication required" });
  }

  if (!user.workspace) {
    throw new HTTPException(403, { message: "Workspace access required" });
  }

  if (user.workspace.status === 'suspended') {
    throw new HTTPException(403, { message: "Workspace is suspended" });
  }

  await next();
}

/**
 * Active subscription middleware - ensures workspace has active subscription
 */
export async function requireActiveSubscription(c: Context, next: Next) {
  const user = c.get("user") as AuthenticatedUser;
  
  if (!user?.workspace) {
    throw new HTTPException(403, { message: "Workspace access required" });
  }

  const allowedPlans = ['trial', 'basic', 'professional', 'enterprise'];
  if (!allowedPlans.includes(user.workspace.subscriptionPlan)) {
    throw new HTTPException(403, { message: "Active subscription required" });
  }

  await next();
}

/**
 * Workspace ownership middleware - ensures user is workspace owner/admin
 */
export function requireWorkspaceAdmin() {
  return requireRole('owner', 'admin');
}

/**
 * Helper to get current user from context
 */
export function getCurrentUser(c: Context): AuthenticatedUser {
  const user = c.get("user") as AuthenticatedUser;
  if (!user) {
    throw new HTTPException(401, { message: "Authentication required" });
  }
  return user;
}

/**
 * Helper to get current workspace from context
 */
export function getCurrentWorkspace(c: Context) {
  const user = getCurrentUser(c);
  if (!user.workspace) {
    throw new HTTPException(403, { message: "Workspace access required" });
  }
  return user.workspace;
}

/**
 * Helper to get current user profile from context
 */
export function getCurrentUserProfile(c: Context) {
  const user = getCurrentUser(c);
  if (!user.profile) {
    throw new HTTPException(403, { message: "User profile not found" });
  }
  return user.profile;
}

/**
 * Check if user has specific role
 */
export function hasRole(user: AuthenticatedUser, ...roles: string[]): boolean {
  return user.profile ? roles.includes(user.profile.role) : false;
}

/**
 * Check if user is workspace admin (owner or admin)
 */
export function isWorkspaceAdmin(user: AuthenticatedUser): boolean {
  return hasRole(user, 'owner', 'admin');
}

/**
 * Check if user can access resource in workspace
 */
export function canAccessWorkspace(user: AuthenticatedUser, workspaceId: string): boolean {
  return user.workspace?.id === workspaceId;
}

// Default export for convenience
export const auth = authMiddleware;
export default authMiddleware;