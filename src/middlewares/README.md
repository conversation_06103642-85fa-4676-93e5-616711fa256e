# Authentication and Validation Middleware

This directory contains comprehensive authentication, authorization, and validation middleware for the multi-tenant business brokerage API.

## Authentication Middleware (`auth.ts`)

### Core Features

- **JWT Token Validation**: Validates Supabase JWT tokens
- **User Context Loading**: Automatically loads user profile and workspace information
- **Role-Based Authorization**: Provides role-based access control
- **Workspace Context**: Ensures users have proper workspace access

### Usage Examples

#### Basic Authentication

```typescript
import { authMiddleware } from '@/middlewares/auth';

// Require authentication for all routes
app.use('*', authMiddleware);

// Or for specific routes
app.use('/api/protected/*', authMiddleware);
```

#### Role-Based Authorization

```typescript
import { requireRole, requireWorkspaceAdmin } from '@/middlewares/auth';

// Require specific roles
app.use('/api/admin/*', requireRole('admin', 'owner'));

// Require workspace admin privileges
app.use('/api/workspace/settings/*', requireWorkspaceAdmin());
```

#### Workspace Access Control

```typescript
import { requireWorkspace, requireActiveSubscription } from '@/middlewares/auth';

// Ensure user belongs to a workspace
app.use('/api/workspace/*', requireWorkspace);

// Ensure workspace has active subscription
app.use('/api/premium/*', requireActiveSubscription);
```

### Helper Functions

```typescript
import { 
  getCurrentUser, 
  getCurrentWorkspace, 
  getCurrentUserProfile,
  hasRole,
  isWorkspaceAdmin,
  canAccessWorkspace 
} from '@/middlewares/auth';

// In route handlers
export const handler = async (c) => {
  const user = getCurrentUser(c);
  const workspace = getCurrentWorkspace(c);
  const profile = getCurrentUserProfile(c);
  
  // Check permissions
  if (hasRole(user, 'admin')) {
    // Admin logic
  }
  
  if (isWorkspaceAdmin(user)) {
    // Workspace admin logic
  }
};
```

## Validation Middleware (`validation.ts`)

### Request Validation

```typescript
import { validateBody, validateQuery, validateParams } from '@/middlewares/validation';
import { z } from 'zod';

const createUserSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
});

// Validate request body
app.post('/users', validateBody(createUserSchema), handler);

// Validate query parameters
const searchSchema = z.object({
  q: z.string().optional(),
  page: z.coerce.number().min(1).default(1),
});

app.get('/search', validateQuery(searchSchema), handler);
```

### Common Validation Schemas

```typescript
import { commonSchemas } from '@/middlewares/validation';

// Use pre-built schemas
app.get('/users', validateQuery(commonSchemas.pagination), handler);
app.get('/reports', validateQuery(commonSchemas.dateRange), handler);
```

### File Upload Validation

```typescript
import { validateFileUpload } from '@/middlewares/validation';

app.post('/upload', 
  validateFileUpload({
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png'],
    required: true
  }), 
  handler
);
```

### Rate Limiting

```typescript
import { validateRateLimit } from '@/middlewares/validation';

// 100 requests per 15 minutes
app.use('/api/*', validateRateLimit(100, 15 * 60 * 1000));
```

## Auth Utils (`../lib/auth-utils.ts`)

### Safe Context Access

```typescript
import { 
  getAuthenticatedUser,
  getUserWorkspace,
  getUserProfile,
  getValidatedBody 
} from '@/lib/auth-utils';

export const handler = async (c) => {
  // These functions throw appropriate HTTP exceptions if data is missing
  const user = getAuthenticatedUser(c);
  const workspace = getUserWorkspace(c);
  const profile = getUserProfile(c);
  const body = getValidatedBody<CreateUserRequest>(c);
};
```

### Permission Checking

```typescript
import { 
  requireUserRole,
  requireWorkspaceAccess,
  requireActiveWorkspace,
  requireAdminRole,
  requireMinimumRole 
} from '@/lib/auth-utils';

export const handler = async (c) => {
  // Check specific roles
  requireUserRole(c, 'admin', 'manager');
  
  // Check workspace access
  requireWorkspaceAccess(c, workspaceId);
  
  // Check workspace status
  requireActiveWorkspace(c);
  
  // Check admin privileges
  requireAdminRole(c);
  
  // Check minimum role level
  requireMinimumRole(c, 'manager');
};
```

### Role Hierarchy

The system uses a role hierarchy for permission checking:

- `owner` (level 4) - Full workspace control
- `admin` (level 3) - Administrative privileges
- `manager` (level 2) - Team management
- `member` (level 1) - Standard access
- `viewer` (level 0) - Read-only access

## Error Handling (`error-handler.ts`)

### Global Error Handler

```typescript
import { errorHandler, notFoundHandler } from '@/middlewares/error-handler';

// Apply global error handling
app.use('*', errorHandler);

// Handle 404s
app.notFound(notFoundHandler);
```

### Error Response Format

All errors follow a consistent format:

```json
{
  "error": "ERROR_CODE",
  "message": "Human readable message",
  "details": [
    {
      "field": "email",
      "message": "Invalid email format",
      "code": "invalid_string"
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/users"
}
```

## Complete Route Example

```typescript
import { createRouter } from '@/lib/create-app';
import { 
  authMiddleware, 
  requireRole, 
  requireWorkspace 
} from '@/middlewares/auth';
import { validateBody, validateQuery } from '@/middlewares/validation';
import { 
  getAuthenticatedUser,
  getUserWorkspace,
  getValidatedBody,
  requireUserRole 
} from '@/lib/auth-utils';

const router = createRouter()
  // Apply authentication to all routes
  .use('*', authMiddleware)
  .use('*', requireWorkspace)
  
  // Public route (within workspace)
  .get('/listings', 
    validateQuery(commonSchemas.pagination),
    async (c) => {
      const user = getAuthenticatedUser(c);
      const workspace = getUserWorkspace(c);
      const query = getValidatedQuery(c);
      
      // Implementation
    }
  )
  
  // Admin-only route
  .post('/listings',
    requireRole('admin', 'manager'),
    validateBody(createListingSchema),
    async (c) => {
      requireUserRole(c, 'admin', 'manager');
      const body = getValidatedBody<CreateListingRequest>(c);
      
      // Implementation
    }
  );
```

## Testing

The middleware includes comprehensive tests. Run them with:

```bash
npm test -- --run src/middlewares/__tests__/
```

## Security Considerations

1. **Token Validation**: All JWT tokens are validated against Supabase
2. **Role Verification**: User roles are verified from the database
3. **Workspace Isolation**: Users can only access their workspace data
4. **Rate Limiting**: Prevents abuse and DoS attacks
5. **Input Validation**: All inputs are validated before processing
6. **Error Handling**: Sensitive information is not leaked in error messages