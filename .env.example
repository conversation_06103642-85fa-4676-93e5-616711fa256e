# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Database Configuration
# Replace with your actual PostgreSQL database URL
# For local development, you might use something like:
# DATABASE_URL=postgresql://username:password@localhost:5432/database_name
# For Supabase, use your Supabase PostgreSQL connection string
DATABASE_URL=postgresql://username:password@localhost:5432/your_database

# Supabase Configuration
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Optional: Service Role Key (for admin operations)
# SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI Configuration
# Get your API key from https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

CORS_ORIGINS=a.com,b.com