# Authentication Debugging Guide

## Issues Fixed

### 1. ✅ Added GET /get-session Endpoint

**Problem**: The GET /get-session endpoint was missing at the root level.

**Solution**: Added a new endpoint in `src/app.ts` that:
- Uses Better Auth's session validation
- Returns `{ session, user }` format as expected
- Handles errors gracefully
- Available at both `/get-session` and `/api/auth/get-session`

### 2. ✅ Cookie Name Verification

**Correct <PERSON>ie Names**: 
- Better Auth with `cookiePrefix: "rendyr"` uses: `rendyr.session_token`
- **NOT** `rendyr.session_data` (this was the mismatch)

**Client Fix Required**: Ensure your client is looking for the correct cookie name:
```javascript
// ❌ Wrong - looking for rendyr.session_data
// ✅ Correct - Better Auth uses rendyr.session_token
```

### 3. ✅ CORS Configuration Verified

**Current CORS Setup**:
- ✅ `Access-Control-Allow-Credentials: true`
- ✅ Exact origins (not wildcard when credentials=true)
- ✅ Proper headers included

**Default Origins**:
- `http://localhost:8080`
- `http://localhost:3001` 
- `https://15a876d3-8f0d-4e15-a589-444086fdc75f.lovableproject.com`
- `https://rendyr.ai`

## Testing the Fixes

### 1. Run the Debug Script

```bash
node debug-auth.js
```

This will test:
- GET /get-session endpoint
- CORS configuration
- Authentication flow
- Cookie handling

### 2. Manual Testing Steps

#### Step 1: Test Session Endpoint
```bash
curl -X GET http://localhost:9999/get-session \
  -H "Content-Type: application/json" \
  --cookie-jar cookies.txt
```

Expected: `{ "session": null, "user": null }`

#### Step 2: Sign In
```bash
curl -X POST http://localhost:9999/v1/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"yourpassword"}' \
  --cookie-jar cookies.txt
```

Expected: Session cookies set in cookies.txt

#### Step 3: Test Authenticated Session
```bash
curl -X GET http://localhost:9999/get-session \
  -H "Content-Type: application/json" \
  --cookie cookies.txt
```

Expected: `{ "session": {...}, "user": {...} }`

#### Step 4: Test Listings API
```bash
curl -X GET http://localhost:9999/v1/listings \
  -H "Content-Type: application/json" \
  --cookie cookies.txt
```

Expected: Listings data (not 401)

### 3. Browser Testing

#### Check Cookies in DevTools
1. Open browser DevTools
2. Go to Application/Storage → Cookies
3. Look for: `rendyr.session_token` (not `rendyr.session_data`)

#### Test CORS
```javascript
// In browser console from your frontend domain
fetch('http://localhost:9999/get-session', {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json'
  }
})
.then(r => r.json())
.then(console.log);
```

## Debugging Output

Added development-mode logging to authentication middleware:
- 🍪 Cookie headers received
- 🔐 Session validation results
- Error details for failed authentication

Check server logs for these debug messages.

## Common Issues & Solutions

### Issue: Still getting 401 errors

**Check**:
1. Cookie name: Must be `rendyr.session_token`
2. Client origin: Must match CORS_ORIGINS exactly
3. Credentials: Must include `credentials: 'include'`
4. HTTPS: In production, cookies require secure connection

### Issue: CORS errors

**Check**:
1. Origin header matches CORS_ORIGINS exactly
2. No trailing slashes in origins
3. Protocol (http/https) matches

### Issue: Session not persisting

**Check**:
1. Cookie settings (httpOnly, secure, sameSite)
2. Domain/path configuration
3. Browser cookie storage

## Environment Variables

Ensure these are set correctly:
```bash
CORS_ORIGINS=http://localhost:3001,https://yourdomain.com
BETTER_AUTH_URL=http://localhost:9999  # or your production URL
BETTER_AUTH_SECRET=your-secret-key
```

## Next Steps

1. Run the debug script to verify all endpoints work
2. Check browser DevTools for correct cookie names
3. Test the complete authentication flow
4. Remove debug logging once issues are resolved
