-- Universal "drop all tables" script for PostgreSQL
-- This will remove all data in user-defined schemas. Use with caution!

-- Drops all tables (ordinary, partitioned, and foreign) in all non-system schemas.
DO $$
DECLARE
    drop_stmt text;
BEGIN
    SELECT
        'DROP TABLE IF EXISTS ' ||
        string_agg(format('%I.%I', schemaname, tablename), ', ' ORDER BY schemaname, tablename) ||
        ' CASCADE;'
    INTO drop_stmt
    FROM pg_tables
    WHERE schemaname NOT IN ('pg_catalog', 'information_schema');

    IF drop_stmt IS NOT NULL THEN
        EXECUTE drop_stmt;
    END IF;
END
$$;

-- Optionally drop standalone sequences that are not owned by any table
DO $$
DECLARE
    drop_seq_stmt text;
BEGIN
    SELECT
        'DROP SEQUENCE IF EXISTS ' ||
        string_agg(format('%I.%I', sequence_schema, sequence_name), ', ' ORDER BY sequence_schema, sequence_name) ||
        ' CASCADE;'
    INTO drop_seq_stmt
    FROM information_schema.sequences
    WHERE sequence_schema NOT IN ('pg_catalog', 'information_schema');

    IF drop_seq_stmt IS NOT NULL THEN
        EXECUTE drop_seq_stmt;
    END IF;
END
$$;