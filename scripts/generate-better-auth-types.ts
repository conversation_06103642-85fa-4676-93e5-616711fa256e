import { writeFile, mkdir } from "node:fs/promises";
import path from "node:path";
import openapiTS, { astToString } from "openapi-typescript";
import { auth } from "../src/lib/auth";

async function main(): Promise<void> {
  // eslint-disable-next-line no-console
  console.log("openapiTS typeof:", typeof openapiTS);
  // Generate OpenAPI schema from Better Auth's built-in API
  const openAPISchema = await auth.api.generateOpenAPISchema();

  // Optional visibility for debugging/piping if needed
  // eslint-disable-next-line no-console
  console.log("schema keys:", Object.keys(openAPISchema ?? {}));

  // Convert schema to TypeScript definitions using openapi-typescript
  const ast = await openapiTS(openAPISchema as unknown as Record<string, unknown>, {
    // Keep defaults; adjust options as needed later
  });
  // eslint-disable-next-line no-console
  console.log("typesContent typeof:", typeof ast);
  // eslint-disable-next-line no-console
  console.log("typesContent keys:", Object.getOwnPropertyNames(ast ?? {}).slice(0, 10));

  const typesString = astToString(ast);

  const outDir = path.resolve(process.cwd(), "src/types");
  const outFile = path.join(outDir, "better-auth-built-in-api.ts");

  await mkdir(outDir, { recursive: true });
  await writeFile(
    outFile,
    `// This file is auto-generated by scripts/generate-better-auth-types.ts\n// Do not edit manually.\n\n${typesString}\n`,
    "utf8",
  );
}

main().catch((error) => {
  // eslint-disable-next-line no-console
  console.error(error);
  process.exitCode = 1;
});


