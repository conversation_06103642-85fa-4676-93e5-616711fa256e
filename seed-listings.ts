import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./src/db/schema";
import { listings, listingDetails, userProfiles, workspaces } from "./src/db/schema";
import { eq } from "drizzle-orm";

// Connect to database
const client = postgres(process.env.DATABASE_URL || "");
const db = drizzle(client, { schema });

// Industries and business types
const INDUSTRIES = [
  'Restaurant & Food Service',
  'Retail & E-commerce',
  'Professional Services',
  'Healthcare & Medical',
  'Automotive Services',
  'Beauty & Personal Care',
  'Real Estate Services',
  'Fitness & Recreation',
  'Education & Training',
  'Manufacturing',
  'Technology Services',
  'Construction & Contracting',
  'Transportation & Logistics',
  'Financial Services',
  'Entertainment & Events',
  'Home Services',
  'Consulting',
  'Agriculture & Farming',
  'Security Services',
  'Cleaning Services'
];

const BUSINESS_STATUSES = ['draft', 'active', 'sold', 'withdrawn'];

const REAL_ESTATE_STATUSES = ['owned', 'leased', 'included', 'negotiable'];

const US_LOCATIONS = [
  'Atlanta, GA', 'Austin, TX', 'Charlotte, NC', 'Chicago, IL', 'Dallas, TX',
  'Denver, CO', 'Houston, TX', 'Las Vegas, NV', 'Los Angeles, CA', 'Miami, FL',
  'New York, NY', 'Orlando, FL', 'Phoenix, AZ', 'San Antonio, TX', 'San Diego, CA',
  'San Francisco, CA', 'Seattle, WA', 'Tampa, FL', 'Philadelphia, PA', 'Boston, MA',
  'Nashville, TN', 'Minneapolis, MN', 'Cleveland, OH', 'Detroit, MI', 'Portland, OR',
  'Kansas City, MO', 'Indianapolis, IN', 'Columbus, OH', 'Sacramento, CA', 'Memphis, TN'
];

// Generate business names by industry
const generateBusinessName = (industry: string): string => {
  const prefixes = ['Elite', 'Premier', 'Golden', 'Prime', 'Royal', 'Diamond', 'Platinum', 'Supreme', 'Metro', 'Urban'];
  const suffixes = ['Solutions', 'Services', 'Group', 'Company', 'Enterprises', 'Partners', 'Associates', 'Corp', 'LLC', 'Inc'];
  
  const industrySpecific: Record<string, string[]> = {
    'Restaurant & Food Service': ['Bistro', 'Café', 'Kitchen', 'Grill', 'Diner', 'Restaurant', 'Eatery'],
    'Retail & E-commerce': ['Store', 'Shop', 'Boutique', 'Market', 'Outlet', 'Gallery', 'Emporium'],
    'Healthcare & Medical': ['Clinic', 'Medical Center', 'Health', 'Wellness', 'Care', 'Practice'],
    'Automotive Services': ['Auto', 'Motors', 'Garage', 'Service Center', 'Automotive'],
    'Beauty & Personal Care': ['Salon', 'Spa', 'Beauty', 'Studio', 'Wellness Center'],
    'Professional Services': ['Consulting', 'Advisory', 'Professional', 'Services', 'Solutions'],
    'Fitness & Recreation': ['Fitness', 'Gym', 'Studio', 'Sports', 'Recreation Center'],
    'Technology Services': ['Tech', 'Digital', 'Software', 'Systems', 'IT Services'],
  };

  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const industryTerms = industrySpecific[industry] || ['Business', 'Company'];
  const industryTerm = industryTerms[Math.floor(Math.random() * industryTerms.length)];
  const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];

  return Math.random() > 0.5 
    ? `${prefix} ${industryTerm}`
    : `${industryTerm} ${suffix}`;
};

// Generate realistic financial data
const generateFinancials = (industry: string) => {
  const baseRevenue = (() => {
    switch (industry) {
      case 'Restaurant & Food Service': return 400000 + Math.random() * 800000;
      case 'Healthcare & Medical': return 800000 + Math.random() * 1500000;
      case 'Automotive Services': return 500000 + Math.random() * 1000000;
      case 'Professional Services': return 300000 + Math.random() * 1200000;
      case 'Technology Services': return 600000 + Math.random() * 2000000;
      case 'Manufacturing': return 1000000 + Math.random() * 3000000;
      default: return 250000 + Math.random() * 750000;
    }
  })();

  const ebitdaMargin = 0.15 + Math.random() * 0.25; // 15-40% EBITDA margin
  const cashFlowSde = baseRevenue * ebitdaMargin;
  const askingPriceMultiple = 2.5 + Math.random() * 2; // 2.5-4.5x SDE
  const askingPrice = cashFlowSde * askingPriceMultiple;

  return {
    annualRevenue: Math.round(baseRevenue),
    cashFlowSde: Math.round(cashFlowSde),
    askingPrice: Math.round(askingPrice),
    ebitda: Math.round(cashFlowSde * 0.9), // Slightly lower than SDE
    inventoryValue: Math.round(baseRevenue * 0.1 + Math.random() * baseRevenue * 0.15)
  };
};

// Generate business description
const generateBusinessDescription = (businessName: string, industry: string) => {
  const templates = {
    'Restaurant & Food Service': `${businessName} is an established ${industry.toLowerCase()} business with a loyal customer base and excellent reputation in the community. The restaurant features a full commercial kitchen, dining area, and outdoor seating. Known for fresh, quality ingredients and exceptional service.`,
    'Retail & E-commerce': `${businessName} is a well-established ${industry.toLowerCase()} operation with both physical and online presence. Strong brand recognition, established supplier relationships, and a proven track record of profitability.`,
    'Healthcare & Medical': `${businessName} is a thriving ${industry.toLowerCase()} practice with an established patient base and excellent reputation. Modern equipment, skilled staff, and strong referral network contribute to consistent growth.`,
    'Professional Services': `${businessName} provides comprehensive ${industry.toLowerCase()} to a diverse client base. Strong reputation, recurring revenue streams, and experienced team make this an excellent acquisition opportunity.`,
    'Technology Services': `${businessName} is an innovative ${industry.toLowerCase()} company serving businesses with cutting-edge solutions. Scalable business model, recurring contracts, and growth potential in expanding market.`,
    default: `${businessName} is a well-established business in the ${industry.toLowerCase()} sector with strong fundamentals and growth potential. Excellent reputation, experienced management team, and solid financial performance.`
  };

  return templates[industry as keyof typeof templates] || templates.default;
};

// Generate detailed financial information
const generateDetailedFinancials = (baseFinancials: any) => ({
  revenue_2023: baseFinancials.annualRevenue,
  revenue_2022: Math.round(baseFinancials.annualRevenue * (0.85 + Math.random() * 0.3)),
  revenue_2021: Math.round(baseFinancials.annualRevenue * (0.75 + Math.random() * 0.4)),
  ebitda: baseFinancials.ebitda,
  inventory_value: baseFinancials.inventoryValue,
  assets_included: [
    'Equipment and Machinery',
    'Furniture and Fixtures',
    'Inventory',
    'Customer Database',
    'Intellectual Property',
    'Vendor Relationships'
  ].filter(() => Math.random() > 0.3),
  additional_financial_info: {
    gross_margin: Math.round((0.35 + Math.random() * 0.3) * 100) / 100,
    operating_expenses: Math.round(baseFinancials.annualRevenue * (0.4 + Math.random() * 0.2)),
    rent_expense: Math.round(baseFinancials.annualRevenue * (0.08 + Math.random() * 0.12)),
    payroll_expense: Math.round(baseFinancials.annualRevenue * (0.25 + Math.random() * 0.15))
  }
});

// Generate operations data
const generateOperations = (industry: string) => {
  const businessModels = {
    'Restaurant & Food Service': ['Dine-in Restaurant', 'Fast Casual', 'Catering Service', 'Food Truck'],
    'Retail & E-commerce': ['B2C Retail', 'B2B Sales', 'E-commerce', 'Wholesale Distribution'],
    'Professional Services': ['Fee-for-Service', 'Retainer Model', 'Project-Based', 'Recurring Subscriptions'],
    'Healthcare & Medical': ['Fee-for-Service', 'Insurance Billing', 'Cash Practice', 'Preventive Care'],
    default: ['Service-Based', 'Product Sales', 'Subscription Model', 'Commission-Based']
  };

  const keyFeatures = [
    'Established Customer Base',
    'Proven Business Model',
    'Strong Brand Recognition',
    'Experienced Staff',
    'Prime Location',
    'Modern Equipment',
    'Efficient Operations',
    'Digital Presence',
    'Quality Products/Services',
    'Competitive Pricing'
  ];

  const competitiveAdvantages = [
    'Market Leader in Local Area',
    'Long-term Customer Relationships',
    'Unique Service Offerings',
    'Cost-effective Operations',
    'Strategic Location',
    'Experienced Management',
    'Strong Vendor Relationships',
    'Brand Recognition',
    'Operational Efficiency',
    'Customer Loyalty Programs'
  ];

  const models = businessModels[industry as keyof typeof businessModels] || businessModels.default;
  
  return {
    business_model: models[Math.floor(Math.random() * models.length)],
    key_features: keyFeatures.sort(() => 0.5 - Math.random()).slice(0, 4 + Math.floor(Math.random() * 3)),
    competitive_advantages: competitiveAdvantages.sort(() => 0.5 - Math.random()).slice(0, 3 + Math.floor(Math.random() * 3)),
    operational_details: {
      hours_of_operation: '6 days/week, 50-60 hours',
      seasonal_trends: 'Consistent year-round with slight seasonal variations',
      key_processes: 'Standardized procedures and training programs'
    }
  };
};

// Generate lease details
const generateLeaseDetails = (realEstateStatus: string) => {
  if (realEstateStatus === 'owned') {
    return {
      lease_terms: 'Property is owned - no lease',
      monthly_rent: null,
      lease_expiration: null,
      renewal_options: 'N/A - Owned Property',
      landlord_info: {}
    };
  }

  return {
    lease_terms: `${5 + Math.floor(Math.random() * 10)} year lease with ${Math.floor(Math.random() * 2) + 1} renewal options`,
    monthly_rent: Math.round(3000 + Math.random() * 15000),
    lease_expiration: new Date(Date.now() + (1 + Math.random() * 8) * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    renewal_options: `${Math.floor(Math.random() * 2) + 1} x ${5} year options`,
    landlord_info: {
      type: 'Commercial Property Management',
      relationship: 'Good relationship with landlord'
    }
  };
};

async function seedListings() {
  console.log('🌱 Starting to seed listings...');

  // Use specific workspace and user IDs
  const WORKSPACE_ID = '54ba8ad9-1604-4cb3-a9b0-ddaeb1723f85';
  const USER_ID = 'c2bdef5f-ec56-4acf-be77-f1bd483edc5a';

  try {
    // Verify the workspace exists
    const workspaceResult = await db
      .select()
      .from(workspaces)
      .where(eq(workspaces.id, WORKSPACE_ID))
      .limit(1);

    if (workspaceResult.length === 0) {
      console.error(`❌ Workspace ${WORKSPACE_ID} not found.`);
      process.exit(1);
    }

    const workspace = workspaceResult[0];
    console.log(`📍 Using workspace: ${workspace.companyName} (${workspace.id})`);

    // Verify the user exists and belongs to the workspace
    const userResult = await db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.userId, USER_ID))
      .limit(1);

    if (userResult.length === 0) {
      console.error(`❌ User ${USER_ID} not found.`);
      process.exit(1);
    }

    const user = userResult[0];
    
    if (user.workspaceId !== WORKSPACE_ID) {
      console.error(`❌ User ${USER_ID} does not belong to workspace ${WORKSPACE_ID}.`);
      process.exit(1);
    }

    console.log(`👤 Using user: ${user.displayName} (${user.userId})`);

    // ✨ Clear existing data first
    console.log('🧹 Clearing existing listings and details...');
    
    // Delete listing details first (due to foreign key constraints)
    const deletedDetailsResult = await db
      .delete(listingDetails)
      .returning({ id: listingDetails.id });
    
    console.log(`🗑️ Deleted ${deletedDetailsResult.length} existing listing details`);

    // Delete listings (filter by workspace to be safe)
    const deletedListingsResult = await db
      .delete(listings)
      .where(eq(listings.workspaceId, WORKSPACE_ID))
      .returning({ id: listings.id, businessName: listings.businessName });
    
    console.log(`🗑️ Deleted ${deletedListingsResult.length} existing listings`);

    // Generate and insert 50 listings
    const listingsToCreate = [];
    const detailsToCreate = [];

    for (let i = 0; i < 50; i++) {
      const industry = INDUSTRIES[Math.floor(Math.random() * INDUSTRIES.length)];
      const businessName = generateBusinessName(industry);
      const location = US_LOCATIONS[Math.floor(Math.random() * US_LOCATIONS.length)];
      const status = BUSINESS_STATUSES[Math.floor(Math.random() * BUSINESS_STATUSES.length)];
      const realEstateStatus = REAL_ESTATE_STATUSES[Math.floor(Math.random() * REAL_ESTATE_STATUSES.length)];
      
      const financials = generateFinancials(industry);
      const employees = Math.floor(2 + Math.random() * 25);
      const yearEstablished = 1990 + Math.floor(Math.random() * 30);
      const ownerHours = Math.floor(20 + Math.random() * 40);
      
      // Calculate days listed (for active listings)
      const daysListed = status === 'active' ? Math.floor(1 + Math.random() * 365) : null;
      const dateListed = status === 'active' 
        ? new Date(Date.now() - (daysListed! * 24 * 60 * 60 * 1000)).toISOString().split('T')[0]
        : null;

      const listingId = crypto.randomUUID();

      // Main listing data
      listingsToCreate.push({
        id: listingId,
        workspaceId: workspace.id,
        createdBy: USER_ID,
        assignedTo: Math.random() > 0.3 ? USER_ID : null,
        businessName,
        industry,
        askingPrice: financials.askingPrice.toString(),
        cashFlowSde: financials.cashFlowSde.toString(),
        annualRevenue: financials.annualRevenue.toString(),
        status,
        generalLocation: location,
        yearEstablished,
        employees,
        ownerHoursWeek: ownerHours,
        dateListed,
        daysListed,
        title: businessName,
        description: `Established ${industry.toLowerCase()} business in ${location}`,
        price: financials.askingPrice.toString(),
        teamVisibility: 'all',
        listingType: 'business_sale',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Detailed listing information
      const detailedFinancials = generateDetailedFinancials(financials);
      const operations = generateOperations(industry);
      const leaseDetails = generateLeaseDetails(realEstateStatus);

      detailsToCreate.push({
        id: crypto.randomUUID(),
        listingId,
        businessDescription: generateBusinessDescription(businessName, industry),
        briefDescription: `${businessName} - Profitable ${industry.toLowerCase()} business with ${employees} employees, established ${yearEstablished}`,
        financialDetails: detailedFinancials,
        operations,
        growthOpportunities: [
          'Expand service offerings',
          'Digital marketing initiatives', 
          'Additional locations',
          'Strategic partnerships',
          'Operational improvements'
        ].sort(() => 0.5 - Math.random()).slice(0, 2 + Math.floor(Math.random() * 3)),
        reasonForSale: [
          'Retirement',
          'Relocation',
          'New business venture',
          'Family reasons',
          'Career change'
        ][Math.floor(Math.random() * 5)],
        trainingPeriod: `${2 + Math.floor(Math.random() * 4)} weeks`,
        supportType: 'Training and ongoing support included',
        financingAvailable: Math.random() > 0.4,
        equipmentHighlights: [
          'Modern equipment included',
          'Recently updated systems',
          'Maintenance records available',
          'Warranty transfers'
        ].filter(() => Math.random() > 0.5),
        supplierRelationships: 'Established supplier network with favorable terms',
        realEstateStatus,
        leaseDetails,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Show progress
      if ((i + 1) % 10 === 0) {
        console.log(`📈 Generated ${i + 1}/50 listings...`);
      }
    }

    // Insert listings in batches
    console.log('💾 Inserting listings into database...');
    
    const insertedListings = await db
      .insert(listings)
      .values(listingsToCreate)
      .returning({ id: listings.id, businessName: listings.businessName });

    console.log(`✅ Inserted ${insertedListings.length} listings`);

    // Insert listing details
    console.log('💾 Inserting listing details...');
    
    const insertedDetails = await db
      .insert(listingDetails)
      .values(detailsToCreate)
      .returning({ id: listingDetails.id });

    console.log(`✅ Inserted ${insertedDetails.length} listing details`);

    // Summary
    console.log('\n🎉 Seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   • Workspace: ${workspace.companyName}`);
    console.log(`   • User: ${user.displayName}`);
    console.log(`   • Listings created: ${insertedListings.length}`);
    console.log(`   • Details created: ${insertedDetails.length}`);
    console.log(`   • Industries covered: ${new Set(listingsToCreate.map(l => l.industry)).size}`);
    
    // Show some sample listings
    console.log('\n📋 Sample listings created:');
    insertedListings.slice(0, 5).forEach((listing, i) => {
      console.log(`   ${i + 1}. ${listing.businessName} (${listing.id})`);
    });

  } catch (error) {
    console.error('❌ Error seeding listings:', error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the seeding function
if (require.main === module) {
  seedListings();
}

export default seedListings; 