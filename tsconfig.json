{"compilerOptions": {"target": "ESNext", "jsx": "react-jsx", "jsxImportSource": "hono/jsx", "baseUrl": "./", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types"], "types": ["node"], "strict": true, "outDir": "./dist", "skipLibCheck": true}, "exclude": ["src/db/migrations/**/*"], "tsc-alias": {"resolveFullPaths": true}}